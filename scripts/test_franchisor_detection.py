"""
Tests for Advanced Franchisor Detection Service

This test suite verifies the OpenAI-based franchisor detection functionality
including embeddings, function calling, and structured outputs.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.franchisor_detection_service import AdvancedFranchisorDetectionService, get_franchisor_detection_service


class TestAdvancedFranchisorDetectionService:
    """Test suite for the advanced franchisor detection service"""
    
    @pytest.fixture
    def mock_session(self):
        """Mock database session"""
        session = Mock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def mock_franchisors(self):
        """Mock franchisor data"""
        return [
            {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Coochie Hydrogreen",
                "description": "Eco-friendly car wash franchise specializing in waterless cleaning",
                "category": "Automotive",
                "region": "Australia",
                "keywords": ["coochie", "hydrogreen", "car", "wash", "eco-friendly", "waterless"],
                "search_text": "Franchise name: Coochie Hydrogreen | Description: Eco-friendly car wash franchise | Category: Automotive"
            },
            {
                "id": "456e7890-e89b-12d3-a456-426614174001",
                "name": "Pizza Palace",
                "description": "Italian pizza restaurant franchise with authentic recipes",
                "category": "Food & Beverage",
                "region": "USA",
                "keywords": ["pizza", "palace", "italian", "restaurant", "food"],
                "search_text": "Franchise name: Pizza Palace | Description: Italian pizza restaurant franchise | Category: Food & Beverage"
            }
        ]
    
    @pytest.fixture
    def detection_service(self, mock_session):
        """Create detection service with mocked dependencies"""
        with patch('app.services.franchisor_detection_service.get_settings') as mock_settings:
            mock_settings.return_value.openai_api_key = "test-api-key"
            
            with patch('app.services.franchisor_detection_service.FranchisorRepository'):
                service = AdvancedFranchisorDetectionService(mock_session)
                return service
    
    @pytest.mark.asyncio
    async def test_detect_franchisor_coochie_hydrogreen(self, detection_service, mock_franchisors):
        """Test detection of Coochie Hydrogreen from question"""
        question = "I want to know about Coochie Hydrogreen franchise opportunities"
        
        # Mock the methods
        detection_service._get_active_franchisors_with_cache = AsyncMock(return_value=mock_franchisors)
        detection_service._detect_using_embeddings = AsyncMock(return_value=("123e4567-e89b-12d3-a456-426614174000", 0.85))
        detection_service._detect_using_function_calling = AsyncMock(return_value=("123e4567-e89b-12d3-a456-426614174000", 0.80))
        detection_service._detect_using_structured_output = AsyncMock(return_value=("123e4567-e89b-12d3-a456-426614174000", 0.82))
        detection_service._combine_detection_results = AsyncMock(return_value=("123e4567-e89b-12d3-a456-426614174000", 0.85))
        
        # Test detection
        result = await detection_service.detect_franchisor_from_question(question, include_confidence=True)
        
        assert result is not None
        franchisor_id, confidence = result
        assert franchisor_id == "123e4567-e89b-12d3-a456-426614174000"
        assert confidence >= 0.75  # Above threshold
        
        # Verify methods were called
        detection_service._get_active_franchisors_with_cache.assert_called_once()
        detection_service._detect_using_embeddings.assert_called_once()
        detection_service._detect_using_function_calling.assert_called_once()
        detection_service._detect_using_structured_output.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_detect_franchisor_no_match(self, detection_service, mock_franchisors):
        """Test when no franchisor is detected"""
        question = "What's the weather like today?"
        
        # Mock the methods to return no matches
        detection_service._get_active_franchisors_with_cache = AsyncMock(return_value=mock_franchisors)
        detection_service._detect_using_embeddings = AsyncMock(return_value=None)
        detection_service._detect_using_function_calling = AsyncMock(return_value=None)
        detection_service._detect_using_structured_output = AsyncMock(return_value=None)
        detection_service._combine_detection_results = AsyncMock(return_value=None)
        
        # Test detection
        result = await detection_service.detect_franchisor_from_question(question, include_confidence=True)
        
        assert result is not None
        franchisor_id, confidence = result
        assert franchisor_id is None
        assert confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_detect_franchisor_low_confidence(self, detection_service, mock_franchisors):
        """Test when confidence is below threshold"""
        question = "Tell me about car washing"
        
        # Mock the methods to return low confidence
        detection_service._get_active_franchisors_with_cache = AsyncMock(return_value=mock_franchisors)
        detection_service._detect_using_embeddings = AsyncMock(return_value=("123e4567-e89b-12d3-a456-426614174000", 0.60))
        detection_service._detect_using_function_calling = AsyncMock(return_value=None)
        detection_service._detect_using_structured_output = AsyncMock(return_value=None)
        detection_service._combine_detection_results = AsyncMock(return_value=("123e4567-e89b-12d3-a456-426614174000", 0.60))
        
        # Test detection
        result = await detection_service.detect_franchisor_from_question(question, include_confidence=True)
        
        assert result is not None
        franchisor_id, confidence = result
        assert franchisor_id is None  # Below threshold
        assert confidence == 0.60
    
    def test_extract_keywords_from_franchisor(self, detection_service):
        """Test keyword extraction from franchisor data"""
        mock_franchisor = Mock()
        mock_franchisor.name = "Coochie Hydrogreen"
        mock_franchisor.description = "Eco-friendly car wash franchise specializing in waterless cleaning technology"
        mock_franchisor.category = "Automotive"
        
        keywords = detection_service._extract_keywords_from_franchisor(mock_franchisor)
        
        assert "coochie" in keywords
        assert "hydrogreen" in keywords
        assert "automotive" in keywords
        assert "eco-friendly" in keywords
        assert "waterless" in keywords
    
    def test_create_search_text(self, detection_service):
        """Test search text creation for embeddings"""
        mock_franchisor = Mock()
        mock_franchisor.name = "Coochie Hydrogreen"
        mock_franchisor.description = "Eco-friendly car wash franchise"
        mock_franchisor.category = "Automotive"
        mock_franchisor.region = "Australia"
        
        search_text = detection_service._create_search_text(mock_franchisor)
        
        expected = "Franchise name: Coochie Hydrogreen | Description: Eco-friendly car wash franchise | Category: Automotive | Region: Australia"
        assert search_text == expected
    
    def test_search_franchisors_by_terms(self, detection_service, mock_franchisors):
        """Test searching franchisors by extracted terms"""
        search_terms = ["coochie", "hydrogreen", "car wash"]
        
        result = detection_service._search_franchisors_by_terms(search_terms, mock_franchisors)
        
        assert result is not None
        assert result["name"] == "Coochie Hydrogreen"
        assert result["id"] == "123e4567-e89b-12d3-a456-426614174000"
    
    def test_factory_function(self, mock_session):
        """Test the factory function"""
        with patch('app.services.franchisor_detection_service.get_settings') as mock_settings:
            mock_settings.return_value.openai_api_key = "test-api-key"
            
            with patch('app.services.franchisor_detection_service.FranchisorRepository'):
                service = get_franchisor_detection_service(mock_session)
                assert isinstance(service, AdvancedFranchisorDetectionService)


class TestIntegrationWithWebhooks:
    """Integration tests with webhook processing"""
    
    @pytest.mark.asyncio
    async def test_webhook_integration(self):
        """Test integration with webhook processing"""
        # This would be an integration test that requires a test database
        # and actual OpenAI API calls (or mocked responses)
        
        # Mock question that should detect Coochie Hydrogreen
        question = "I'm interested in Coochie Hydrogreen franchise opportunities in my area"
        
        # In a real integration test, this would:
        # 1. Set up test database with sample franchisors
        # 2. Call the webhook processing function
        # 3. Verify that the correct franchisor is detected
        # 4. Verify that the RAG system uses the detected franchisor
        
        # For now, just verify the test structure
        assert question is not None
        assert "Coochie Hydrogreen" in question


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
