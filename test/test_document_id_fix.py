#!/usr/bin/env python3
"""
Test script to verify the document ID fix for DocQA integration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from docqa.ingest import DocumentIngestionService
import uuid


def test_document_id_parameter():
    """Test that the ingest_document method accepts and uses document_id parameter"""
    print("🧪 Testing document_id parameter in DocQA ingestion...")
    
    try:
        # Initialize the service
        service = DocumentIngestionService()
        
        # Test with provided document_id
        test_document_id = str(uuid.uuid4())
        print(f"✅ Test document ID: {test_document_id}")
        
        # This should not raise an error about missing parameter
        # We're just testing the method signature, not actually processing
        print("✅ ingest_document method accepts document_id parameter")
        
        # Test the method signature
        import inspect
        sig = inspect.signature(service.ingest_document)
        params = list(sig.parameters.keys())
        
        print(f"✅ Method parameters: {params}")
        
        if 'document_id' in params:
            print("✅ document_id parameter is present in method signature")
            return True
        else:
            print("❌ document_id parameter is missing from method signature")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_document_id_usage():
    """Test that document_id is properly used when provided"""
    print("\n🧪 Testing document_id usage logic...")
    
    try:
        # Read the source code to verify the logic
        with open('docqa/ingest.py', 'r') as f:
            content = f.read()
        
        # Check for the key logic
        if 'if document_id is None:' in content:
            print("✅ Found conditional logic for document_id")
        else:
            print("❌ Missing conditional logic for document_id")
            return False
            
        if 'document_id = str(uuid.uuid4())' in content:
            print("✅ Found UUID generation fallback")
        else:
            print("❌ Missing UUID generation fallback")
            return False
            
        print("✅ Document ID usage logic is correct")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_integration_service_update():
    """Test that DocQA integration service passes document_id"""
    print("\n🧪 Testing DocQA integration service update...")
    
    try:
        # Read the integration service source code
        with open('app/services/docqa_integration_service.py', 'r') as f:
            content = f.read()
        
        # Check for the document_id parameter being passed
        if 'document_id=document_id' in content:
            print("✅ Found document_id being passed to ingest_document")
        else:
            print("❌ Missing document_id parameter in integration service")
            return False
            
        print("✅ Integration service correctly passes document_id")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting document ID fix verification tests...\n")
    
    tests = [
        test_document_id_parameter,
        test_document_id_usage,
        test_integration_service_update
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    print("\n📊 Test Results:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎯 All tests passed! The document ID fix should resolve the foreign key constraint violation.")
        print("\n📝 What was fixed:")
        print("1. DocQA ingest_document now accepts optional document_id parameter")
        print("2. Uses provided document_id instead of generating new UUID")
        print("3. Integration service passes actual document ID from database")
        print("4. This ensures document_chunks references existing documents table records")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    main()
