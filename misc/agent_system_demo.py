"""
Multi-Agent System Demo
Demonstrates the LangGraph-based agent system capabilities
"""

import asyncio
from datetime import datetime
from app.agents.orchestrator import AgentOrchestrator


async def main():
    """
    Demo of the multi-agent system
    """
    print("🤖 Initializing Multi-Agent System...")
    
    # Initialize the orchestrator
    orchestrator = AgentOrchestrator()
    
    print("✅ Agent system initialized successfully!")
    print(f"📊 System Status: {orchestrator.get_workflow_status()}")
    
    # Example conversations
    examples = [
        {
            "name": "Greeting and Introduction",
            "message": "Hello! I'm interested in franchise opportunities.",
            "session_id": "demo_session_1"
        },
        {
            "name": "Document Question",
            "message": "What are the investment requirements for <PERSON>'s franchise?",
            "session_id": "demo_session_2"
        },
        {
            "name": "Lead Qualification",
            "message": "My name is <PERSON>, I'm looking to invest around $500,000 in a franchise in Sydney.",
            "session_id": "demo_session_3"
        },
        {
            "name": "Meeting Booking Request",
            "message": "I'd like to schedule a consultation to discuss franchise opportunities.",
            "session_id": "demo_session_4"
        },
        {
            "name": "Out of Scope Query",
            "message": "What's the weather like today?",
            "session_id": "demo_session_5"
        }
    ]
    
    print("\n" + "="*60)
    print("🎯 RUNNING EXAMPLE CONVERSATIONS")
    print("="*60)
    
    for i, example in enumerate(examples, 1):
        print(f"\n📝 Example {i}: {example['name']}")
        print(f"💬 User: {example['message']}")
        print("🤔 Processing...")
        
        try:
            # Process the message
            result = await orchestrator.process_message(
                message=example['message'],
                session_id=example['session_id'],
                context={
                    "user_id": "demo_user",
                    "timestamp": datetime.utcnow().isoformat(),
                    "example_name": example['name']
                }
            )
            
            # Display results
            print(f"🤖 Agent: {result.get('response', 'No response')}")
            print(f"🎯 Intent: {result.get('intent', 'Unknown')}")
            print(f"📍 Execution Path: {' → '.join(result.get('execution_path', []))}")
            
            if result.get('lead_id'):
                print(f"👤 Lead ID: {result['lead_id']}")
            
            if result.get('error'):
                print(f"❌ Error: {result['error']}")
            
        except Exception as e:
            print(f"❌ Error processing example: {str(e)}")
        
        print("-" * 60)
    
    print("\n" + "="*60)
    print("🔧 TESTING INDIVIDUAL AGENTS")
    print("="*60)
    
    # Test individual agents
    agents_to_test = [
        ("conversation", "Hello there!"),
        ("question_answering", "Tell me about franchise fees"),
        ("lead_qualification", "I'm Sarah Johnson from Melbourne"),
        ("meeting_booking", "Can I book a meeting for next Tuesday?")
    ]
    
    for agent_name, test_message in agents_to_test:
        print(f"\n🧪 Testing {agent_name} agent...")
        
        try:
            agent = orchestrator.agents.get(agent_name)
            if agent:
                # Test agent health
                health = await agent.health_check()
                print(f"💚 Health Check: {'✅ Healthy' if health else '❌ Unhealthy'}")
                
                # Get agent status
                status = agent.get_status()
                print(f"📊 Status: {status['status']}")
                print(f"📈 Success Rate: {status['success_rate']:.2%}")
                
            else:
                print(f"❌ Agent '{agent_name}' not found")
                
        except Exception as e:
            print(f"❌ Error testing agent: {str(e)}")
    
    print("\n" + "="*60)
    print("📋 SYSTEM SUMMARY")
    print("="*60)
    
    # Final system status
    final_status = orchestrator.get_workflow_status()
    print(f"🏗️  Workflow Compiled: {final_status['workflow_compiled']}")
    print(f"💾 Checkpointer Enabled: {final_status['checkpointer_enabled']}")
    print(f"🤖 Total Agents: {len(final_status['agents'])}")
    
    print("\n🎉 Demo completed successfully!")
    print("💡 You can now use the FastAPI endpoints to interact with the system:")
    print("   - POST /api/agents/chat - Chat with agents")
    print("   - POST /api/agents/upload-document - Upload PDF documents")
    print("   - GET /api/agents/status - Get system status")
    print("   - POST /api/franchisors/{id}/upload-brochure - Upload PDF brochures")
    print("\n📄 Note: Only PDF files are supported for document ingestion")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
