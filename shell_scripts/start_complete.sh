#!/bin/bash
"""
GrowthHive Complete Startup Script
Handles everything: environment setup, services, database, and application startup
Uses local PostgreSQL and references .env file for configuration
"""

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Header
echo "=================================================================="
print_header "GrowthHive Complete Startup Script"
echo "=================================================================="

# Step 1: Check Prerequisites
print_info "Checking prerequisites..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker Desktop first."
    exit 1
fi
print_status "Docker is running"

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create it with required environment variables."
    print_info "You can copy from .env.example: cp .env.example .env"
    exit 1
fi
print_status ".env file found"

# Load environment variables from .env
set -a
source .env
set +a
print_status "Environment variables loaded from .env"

# Check if virtual environment exists
if [ ! -d "venv311" ]; then
    print_warning "Virtual environment 'venv311' not found. Creating it..."
    python3 -m venv venv311
    print_status "Virtual environment created"
fi

# Step 2: Activate Virtual Environment
print_info "Activating Python virtual environment..."
source venv311/bin/activate
print_status "Virtual environment activated"

# Step 3: Install/Update Dependencies
print_info "Installing/updating Python dependencies..."
pip install -q --upgrade pip
pip install -q -r requirements.txt
pip install -q langgraph langgraph-checkpoint langchain langchain-openai langchain-community langchain-core phonenumbers
print_status "Python dependencies installed"

# Step 4: Check PostgreSQL Connection
print_info "Checking PostgreSQL database connection..."
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL not set in .env file"
    exit 1
fi

# Extract database details from DATABASE_URL for connection test
DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo $DATABASE_URL | sed -n 's/.*\/\([^?]*\).*/\1/p')
DB_USER=$(echo $DATABASE_URL | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')

# Test PostgreSQL connection
if ! python3 -c "
import psycopg
import os
try:
    conn = psycopg.connect(os.getenv('DATABASE_URL').replace('postgresql+asyncpg://', 'postgresql://'))
    conn.close()
    print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
    exit(1)
" > /dev/null 2>&1; then
    print_error "Cannot connect to PostgreSQL database"
    print_info "Please ensure PostgreSQL is running and DATABASE_URL in .env is correct"
    print_info "Current DATABASE_URL: $DATABASE_URL"
    exit 1
fi
print_status "PostgreSQL database connection verified"


# Step 6: Setup pgvector (if needed)
print_info "Setting up pgvector for DocQA..."
if [ -f "setup_docqa.py" ]; then
    python3 setup_docqa.py > /dev/null 2>&1 || print_warning "pgvector setup skipped (may already be configured)"
    print_status "pgvector setup completed"
fi

# Step 7: Create necessary directories
print_info "Creating necessary directories..."
mkdir -p logs
mkdir -p data
mkdir -p uploads
print_status "Directories created"

# Step 8: Stop any existing Docker containers
print_info "Stopping any existing Docker containers..."
docker-compose -f docker-compose.rabbitmq.yml down --remove-orphans > /dev/null 2>&1 || true
print_status "Existing containers stopped"

# Step 9: Start RabbitMQ and Redis
print_info "Starting RabbitMQ and Redis services..."
docker-compose -f docker-compose.rabbitmq.yml up -d rabbitmq redis
sleep 5

# Wait for RabbitMQ to be ready
print_info "Waiting for RabbitMQ to be ready..."
RABBITMQ_READY=false
for i in {1..30}; do
    if docker-compose -f docker-compose.rabbitmq.yml exec -T rabbitmq rabbitmqctl status > /dev/null 2>&1; then
        RABBITMQ_READY=true
        break
    fi
    echo "Waiting for RabbitMQ... ($i/30)"
    sleep 2
done

if [ "$RABBITMQ_READY" = false ]; then
    print_error "RabbitMQ failed to start within 60 seconds"
    exit 1
fi
print_status "RabbitMQ is ready"

# Wait for Redis to be ready
print_info "Waiting for Redis to be ready..."
REDIS_READY=false
for i in {1..15}; do
    if docker-compose -f docker-compose.rabbitmq.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
        REDIS_READY=true
        break
    fi
    echo "Waiting for Redis... ($i/15)"
    sleep 2
done

if [ "$REDIS_READY" = false ]; then
    print_error "Redis failed to start within 30 seconds"
    exit 1
fi
print_status "Redis is ready"

# Step 10: Start Celery Worker
print_info "Starting Celery worker..."
nohup python celery_worker.py --loglevel=info --concurrency=4 --queues=document_processing > logs/celery_worker.log 2>&1 &
CELERY_PID=$!
sleep 3

# Check if Celery worker started successfully
if ps -p $CELERY_PID > /dev/null; then
    print_status "Celery worker started (PID: $CELERY_PID)"
else
    print_error "Failed to start Celery worker"
    exit 1
fi

# Step 11: Verify Application Can Import
print_info "Verifying application imports..."
if ! python3 -c "from app.main import app; print('✅ Application imports successfully')" > /dev/null 2>&1; then
    print_error "Application import failed"
    exit 1
fi
print_status "Application imports verified"

# Step 12: Display Service Information
echo ""
print_header "🎉 All Services Started Successfully!"
echo "=================================================================="
print_info "Service URLs:"
echo "  • Main Application:      http://localhost:8000"
echo "  • API Documentation:     http://localhost:8000/api/docs"
echo "  • Interactive API:       http://localhost:8000/api/redoc"
echo "  • RabbitMQ Management:   http://localhost:15672 (growthhive/growthhive123)"
echo "  • Redis Commander:       http://localhost:8081 (admin/growthhive123)"
echo ""
print_info "Log Files:"
echo "  • Celery Worker:         logs/celery_worker.log"
echo "  • Application:           Will display below"
echo ""
print_warning "Press Ctrl+C to stop all services"
echo "=================================================================="

# Function to cleanup on exit
cleanup() {
    echo ""
    print_info "Shutting down services..."
    
    # Stop Celery worker
    if ps -p $CELERY_PID > /dev/null 2>/dev/null; then
        kill $CELERY_PID
        print_status "Celery worker stopped"
    fi
    
    # Stop Docker containers
    docker-compose -f docker-compose.rabbitmq.yml down > /dev/null 2>&1
    print_status "Docker containers stopped"
    
    print_status "All services stopped successfully"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Step 13: Start FastAPI Application
print_header "Starting FastAPI Application..."
print_info "Starting server on http://localhost:8000"
echo ""

# Start FastAPI server (this will block until Ctrl+C)
python start_server.py
