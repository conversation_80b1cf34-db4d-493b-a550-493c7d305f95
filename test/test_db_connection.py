"""
Test database connection with new credentials
"""

import asyncio
import asyncpg
from app.core.config.settings import settings

async def test_db_connection():
    """Test database connection"""
    
    print("Testing database connection...")
    print(f"Database URL: {settings.DATABASE_URL}")
    
    try:
        # Extract connection details from DATABASE_URL
        # postgresql+asyncpg://khushi:Admin%401234@localhost:5432/GrowthHive
        url_parts = settings.DATABASE_URL.replace("postgresql+asyncpg://", "")
        user_pass, host_db = url_parts.split("@")
        user, password = user_pass.split(":")
        host_port, database = host_db.split("/")
        host, port = host_port.split(":")
        
        # Decode URL-encoded password
        password = password.replace("%40", "@")
        
        print(f"Connecting to:")
        print(f"  Host: {host}")
        print(f"  Port: {port}")
        print(f"  Database: {database}")
        print(f"  User: {user}")
        print(f"  Password: {'*' * len(password)}")
        
        # Test connection
        conn = await asyncpg.connect(
            host=host,
            port=int(port),
            database=database,
            user=user,
            password=password
        )
        
        # Test query
        result = await conn.fetchval("SELECT version()")
        print(f"✅ Database connection successful!")
        print(f"PostgreSQL version: {result}")
        
        # Test if leads table exists
        table_exists = await conn.fetchval(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'leads')"
        )
        
        if table_exists:
            lead_count = await conn.fetchval("SELECT COUNT(*) FROM leads")
            print(f"✅ Leads table found with {lead_count} records")
        else:
            print("⚠️ Leads table not found")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_db_connection())
    if success:
        print("\n🎉 Database connection is working!")
    else:
        print("\n💥 Database connection failed. Please check your credentials.")
