#!/usr/bin/env python3
"""
Simple QnA interface using the existing docqa.py system.
"""

import os
import subprocess


def run_docqa_command(command_args):
    """Run a docqa.py command and return the output."""
    try:
        cmd = ["python", "docqa.py"] + command_args
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            env={**os.environ, "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY")}
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)


def ingest_document():
    """Ingest the PDF document."""
    print("🔄 Ingesting PDF document...")
    s3_url = "s3://openxcell-development-public/growthhive/brochure/20250701_113703_07ce376fa750.pdf"
    
    success, stdout, stderr = run_docqa_command(["ingest", s3_url, "--force"])
    
    if success:
        print("✅ Document ingested successfully!")
        print(stdout)
        return True
    else:
        print("❌ Document ingestion failed!")
        print(f"Error: {stderr}")
        return False


def ask_question(question):
    """Ask a question about the document."""
    print(f"\n🤔 Question: {question}")
    print("🔍 Searching for answer...")
    
    success, stdout, stderr = run_docqa_command(["ask", question, "--no-stream"])
    
    if success:
        print(stdout)
        return stdout
    else:
        print(f"❌ Error: {stderr}")
        return None


def interactive_qna():
    """Start interactive Q&A session."""
    print("\n🎯 Interactive Q&A Session Started!")
    print("💡 Ask questions about the franchise document. Type 'quit' to exit.")
    print("=" * 60)
    
    while True:
        try:
            question = input("\n❓ Your question: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not question:
                print("Please enter a question.")
                continue
            
            ask_question(question)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def main():
    """Main function."""
    print("🤖 Simple QnA System for Franchise Document")
    print("=" * 50)
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        return
    
    print(f"✅ OpenAI API Key found: {api_key[:10]}...")
    
    # Ingest document
    if not ingest_document():
        return
    
    # Demo questions
    demo_questions = [
        "What is this document about?",
        "What are the upfront costs for this franchise?",
        "What are the financial projections?",
        "What is included in the SWOT analysis?",
        "What services does this franchise provide?"
    ]
    
    print("\n🎯 Demo Questions:")
    print("=" * 30)
    
    for question in demo_questions:
        ask_question(question)
        print("\n" + "-" * 50)
    
    # Start interactive session
    interactive_qna()


if __name__ == "__main__":
    main()
