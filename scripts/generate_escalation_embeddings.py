#!/usr/bin/env python3
"""
Script to generate embeddings for existing escalation questions
"""
import asyncio
import os
import sys
from openai import OpenAI
from sqlalchemy import text

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.core.database.connection import get_db

def generate_embedding(text: str) -> list:
    """Generate embedding using OpenAI"""
    try:
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        response = client.embeddings.create(
            model="text-embedding-ada-002",
            input=text.strip()
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Error generating embedding: {e}")
        return []

async def update_escalation_embeddings():
    """Generate embeddings for all escalation questions without embeddings"""
    async for db in get_db():
        try:
            # Get questions without embeddings
            query = text("""
                SELECT id, name 
                FROM escalation_question_bank 
                WHERE embedding IS NULL 
                AND is_active = true 
                AND is_deleted = false
                ORDER BY created_at DESC
            """)
            
            result = await db.execute(query)
            questions = result.fetchall()
            
            print(f"Found {len(questions)} questions without embeddings")
            
            for question in questions:
                question_id = question.id
                question_text = question.name
                
                print(f"Generating embedding for: {question_text[:50]}...")
                
                # Generate embedding
                embedding = generate_embedding(question_text)
                if embedding:
                    # Update the question with embedding using raw SQL
                    embedding_str = str(embedding).replace("'", '"')  # Convert to proper JSON format
                    update_query = text(f"""
                        UPDATE escalation_question_bank
                        SET embedding = '{embedding_str}'::vector
                        WHERE id = '{question_id}'
                    """)

                    await db.execute(update_query)
                    
                    print(f"✅ Updated embedding for question: {question_id}")
                else:
                    print(f"❌ Failed to generate embedding for question: {question_id}")
            
            await db.commit()
            print(f"✅ Successfully processed {len(questions)} questions")
            
        except Exception as e:
            print(f"Error updating embeddings: {e}")
            await db.rollback()
        finally:
            break

if __name__ == "__main__":
    asyncio.run(update_escalation_embeddings())
