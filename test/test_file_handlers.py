#!/usr/bin/env python3
"""
Test script for file handlers
"""

import sys
import os
from pathlib import Path
import tempfile
from PIL import Image, ImageDraw, ImageFont

# Add project root to path
sys.path.append('.')

from docqa.file_handlers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ImageHandler


def create_test_image_with_text():
    """Create a test image with text for OCR testing"""
    # Create a white image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Add some text
    text = "This is a test image\nwith multiple lines\nfor OCR testing"
    
    try:
        # Try to use a default font
        font = ImageFont.load_default()
    except:
        font = None
    
    # Draw text
    draw.text((20, 50), text, fill='black', font=font)
    
    return img


def test_pdf_handler():
    """Test PDF handler"""
    print("\n🔍 Testing PDF Handler...")
    
    try:
        handler = PDFHandler()
        print(f"✅ PDFHandler created: {handler.supported_extensions}")
        
        # Test with a non-existent file
        fake_path = Path("nonexistent.pdf")
        can_handle = handler.can_handle(fake_path)
        print(f"✅ Can handle .pdf: {can_handle}")
        
        # Test validation with non-existent file
        result = handler.process_file(fake_path)
        print(f"✅ Validation works: {not result.success}")
        
    except Exception as e:
        print(f"❌ PDF Handler test failed: {e}")


def test_docx_handler():
    """Test DOCX handler"""
    print("\n🔍 Testing DOCX Handler...")
    
    try:
        handler = DocxHandler()
        print(f"✅ DocxHandler created: {handler.supported_extensions}")
        
        # Test file type detection
        docx_path = Path("test.docx")
        doc_path = Path("test.doc")
        
        print(f"✅ Can handle .docx: {handler.can_handle(docx_path)}")
        print(f"✅ Can handle .doc: {handler.can_handle(doc_path)}")
        
    except Exception as e:
        print(f"❌ DOCX Handler test failed: {e}")


def test_image_handler():
    """Test Image handler with actual image"""
    print("\n🔍 Testing Image Handler...")
    
    try:
        handler = ImageHandler()
        print(f"✅ ImageHandler created: {handler.supported_extensions}")
        
        # Create a test image
        test_img = create_test_image_with_text()
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            test_img.save(tmp_file.name)
            tmp_path = Path(tmp_file.name)
        
        try:
            # Test if handler can handle the file
            can_handle = handler.can_handle(tmp_path)
            print(f"✅ Can handle .png: {can_handle}")
            
            # Test processing
            result = handler.process_file(tmp_path)
            print(f"✅ Processing result: success={result.success}")
            
            if result.success:
                print(f"✅ Extracted text length: {len(result.text_content)}")
                print(f"✅ Text preview: {result.text_content[:100]}...")
            else:
                print(f"⚠️  Processing failed: {result.error_message}")
                
        finally:
            # Clean up
            try:
                tmp_path.unlink()
            except:
                pass
                
    except Exception as e:
        print(f"❌ Image Handler test failed: {e}")


def test_file_handler_integration():
    """Test file handler integration"""
    print("\n🔍 Testing File Handler Integration...")
    
    try:
        from docqa.file_handlers import BaseFileHandler
        
        # Test base handler functionality
        base_handler = BaseFileHandler()
        
        # Test text cleaning
        dirty_text = "  This   is    dirty   text  \n\n\n  with   extra   spaces  \n\n"
        cleaned = base_handler.clean_text(dirty_text)
        print(f"✅ Text cleaning works: '{cleaned}'")
        
        # Test chunking
        long_text = "This is a test sentence. " * 100
        chunks = base_handler.chunk_text(long_text, chunk_size=50, overlap=10)
        print(f"✅ Text chunking: {len(chunks)} chunks created")
        
        if chunks:
            print(f"✅ First chunk length: {len(chunks[0].split())} words")
            print(f"✅ Last chunk length: {len(chunks[-1].split())} words")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")


def test_error_handling():
    """Test error handling in file handlers"""
    print("\n🔍 Testing Error Handling...")
    
    handlers = [
        ("PDF", PDFHandler()),
        ("DOCX", DocxHandler()),
        ("Image", ImageHandler())
    ]
    
    for name, handler in handlers:
        try:
            # Test with non-existent file
            fake_file = Path(f"nonexistent.{handler.supported_extensions[0][1:]}")
            result = handler.process_file(fake_file)
            
            if not result.success:
                print(f"✅ {name} handler properly handles missing files")
            else:
                print(f"⚠️  {name} handler should fail with missing files")
                
        except Exception as e:
            print(f"❌ {name} handler error test failed: {e}")


def main():
    """Run all tests"""
    print("🚀 File Handlers Test Suite")
    print("=" * 50)
    
    # Check environment
    print("\n📋 Environment Check:")
    
    # Check OpenAI API key
    if os.getenv("OPENAI_API_KEY"):
        print("✅ OPENAI_API_KEY is set")
    else:
        print("⚠️  OPENAI_API_KEY not set (needed for chart analysis)")
    
    # Check database URL
    if os.getenv("DATABASE_URL"):
        print("✅ DATABASE_URL is set")
    else:
        print("⚠️  DATABASE_URL not set (needed for full system)")
    
    # Run tests
    test_pdf_handler()
    test_docx_handler()
    test_image_handler()
    test_file_handler_integration()
    test_error_handling()
    
    print("\n🎉 File Handler Tests Completed!")
    print("\nNext steps:")
    print("1. Test with real files:")
    print("   python3 -c \"from docqa.file_handlers import PDFHandler; h=PDFHandler(); print(h.process_file(Path('your_file.pdf')))\"")
    print("2. Test full ingestion:")
    print("   python3 -m docqa.cli ingest /path/to/your/document.pdf")


if __name__ == "__main__":
    main()
