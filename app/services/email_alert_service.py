"""
Robust Email Alert Service for GrowthHive
Handles critical error notifications and system alerts
"""

import asyncio
import traceback
from datetime import datetime, timezone
from email.message import EmailMessage
from typing import Optional, Dict, Any, List
from enum import Enum

import aiosmtplib
from app.core.config.settings import settings
from app.core.logging import logger


class AlertLevel(str, Enum):
    """Alert severity levels"""
    CRITICAL = "CRITICAL"
    ERROR = "ERROR"
    WARNING = "WARNING"
    INFO = "INFO"


class AlertType(str, Enum):
    """Types of alerts"""
    SYSTEM_ERROR = "SYSTEM_ERROR"
    DATABASE_ERROR = "DATABASE_ERROR"
    API_ERROR = "API_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    SECURITY_ALERT = "SECURITY_ALERT"
    PERFORMANCE_ALERT = "PERFORMANCE_ALERT"
    CUSTOM = "CUSTOM"


class EmailAlertService:
    """
    Robust email alert service for sending critical notifications
    Follows GrowthHive patterns and conventions
    """
    
    def __init__(self):
        self.enabled = settings.EMAIL_ALERTS_ENABLED
        self.sender_email = settings.ALERT_EMAIL_SENDER
        self.sender_password = settings.ALERT_EMAIL_PASSWORD
        self.recipient_email = settings.ALERT_EMAIL_RECIPIENT
        self.smtp_server = settings.ALERT_SMTP_SERVER
        self.smtp_port = settings.ALERT_SMTP_PORT
        self.subject_prefix = settings.ALERT_EMAIL_SUBJECT_PREFIX
        
        # Rate limiting to prevent spam
        self._last_alert_times: Dict[str, datetime] = {}
        self._alert_cooldown_minutes = 5  # Minimum time between similar alerts
        
    async def send_critical_alert(
        self,
        title: str,
        description: str,
        alert_type: AlertType = AlertType.SYSTEM_ERROR,
        additional_data: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None
    ) -> bool:
        """
        Send a critical alert email
        
        Args:
            title: Alert title
            description: Alert description
            alert_type: Type of alert
            additional_data: Additional context data
            exception: Exception object if applicable
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if not self.enabled:
            logger.info("Email alerts are disabled")
            return False
            
        try:
            # Check rate limiting
            alert_key = f"{alert_type}_{title}"
            if self._is_rate_limited(alert_key):
                logger.info(f"Alert rate limited: {alert_key}")
                return False
                
            # Create email content
            subject = f"{self.subject_prefix} - {alert_type.value}: {title}"
            body = self._create_alert_body(
                title=title,
                description=description,
                alert_type=alert_type,
                additional_data=additional_data,
                exception=exception
            )
            
            # Send email
            success = await self._send_email(subject, body)
            
            if success:
                self._update_rate_limit(alert_key)
                logger.info(f"Critical alert sent successfully: {title}")
            else:
                logger.error(f"Failed to send critical alert: {title}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error in send_critical_alert: {e}")
            return False
    
    async def send_exception_alert(
        self,
        exception: Exception,
        request_path: Optional[str] = None,
        request_method: Optional[str] = None,
        user_id: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send an alert for unhandled exceptions
        
        Args:
            exception: The exception that occurred
            request_path: API endpoint path
            request_method: HTTP method
            user_id: User ID if available
            additional_context: Additional context information
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            title = f"Unhandled Exception: {type(exception).__name__}"
            description = str(exception)
            
            context_data = {
                "exception_type": type(exception).__name__,
                "exception_message": str(exception),
                "traceback": traceback.format_exc(),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "environment": settings.ENVIRONMENT
            }
            
            if request_path:
                context_data["request_path"] = request_path
            if request_method:
                context_data["request_method"] = request_method
            if user_id:
                context_data["user_id"] = user_id
            if additional_context:
                context_data.update(additional_context)
                
            return await self.send_critical_alert(
                title=title,
                description=description,
                alert_type=AlertType.SYSTEM_ERROR,
                additional_data=context_data,
                exception=exception
            )
            
        except Exception as e:
            logger.error(f"Error in send_exception_alert: {e}")
            return False
    
    async def send_security_alert(
        self,
        event: str,
        description: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send a security-related alert
        
        Args:
            event: Security event name
            description: Event description
            user_id: User ID if applicable
            ip_address: IP address if applicable
            additional_data: Additional security context
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            context_data = {
                "security_event": event,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "environment": settings.ENVIRONMENT
            }
            
            if user_id:
                context_data["user_id"] = user_id
            if ip_address:
                context_data["ip_address"] = ip_address
            if additional_data:
                context_data.update(additional_data)
                
            return await self.send_critical_alert(
                title=f"Security Alert: {event}",
                description=description,
                alert_type=AlertType.SECURITY_ALERT,
                additional_data=context_data
            )
            
        except Exception as e:
            logger.error(f"Error in send_security_alert: {e}")
            return False
    
    def _is_rate_limited(self, alert_key: str) -> bool:
        """Check if alert is rate limited"""
        if alert_key not in self._last_alert_times:
            return False
            
        last_time = self._last_alert_times[alert_key]
        time_diff = datetime.now(timezone.utc) - last_time
        return time_diff.total_seconds() < (self._alert_cooldown_minutes * 60)
    
    def _update_rate_limit(self, alert_key: str) -> None:
        """Update rate limit timestamp"""
        self._last_alert_times[alert_key] = datetime.now(timezone.utc)
    
    def _create_alert_body(
        self,
        title: str,
        description: str,
        alert_type: AlertType,
        additional_data: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None
    ) -> str:
        """Create formatted email body for alerts"""
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        
        body_parts = [
            "🚨 GROWTHHIVE SYSTEM ALERT 🚨",
            "=" * 50,
            f"Alert Type: {alert_type.value}",
            f"Title: {title}",
            f"Timestamp: {timestamp}",
            f"Environment: {settings.ENVIRONMENT}",
            "",
            "DESCRIPTION:",
            "-" * 20,
            description,
            ""
        ]
        
        if exception:
            body_parts.extend([
                "EXCEPTION DETAILS:",
                "-" * 20,
                f"Type: {type(exception).__name__}",
                f"Message: {str(exception)}",
                "",
                "TRACEBACK:",
                "-" * 20,
                traceback.format_exc(),
                ""
            ])
        
        if additional_data:
            body_parts.extend([
                "ADDITIONAL DATA:",
                "-" * 20
            ])
            for key, value in additional_data.items():
                body_parts.append(f"{key}: {value}")
            body_parts.append("")
        
        body_parts.extend([
            "=" * 50,
            "This is an automated alert from GrowthHive system.",
            "Please investigate and take appropriate action.",
            "",
            f"System: {settings.PROJECT_NAME} v{settings.VERSION}",
            f"Host: {settings.HOST}:{settings.PORT}"
        ])
        
        return "\n".join(body_parts)
    
    async def _send_email(self, subject: str, body: str) -> bool:
        """
        Send email using aiosmtplib
        
        Args:
            subject: Email subject
            body: Email body
            
        Returns:
            bool: True if sent successfully, False otherwise
        """
        try:
            # Create email message
            message = EmailMessage()
            message["From"] = self.sender_email
            message["To"] = self.recipient_email
            message["Subject"] = subject
            message.set_content(body)
            
            # Send email with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    await aiosmtplib.send(
                        message,
                        hostname=self.smtp_server,
                        port=self.smtp_port,
                        username=self.sender_email,
                        password=self.sender_password,
                        use_tls=True,
                        timeout=30
                    )
                    logger.info(f"Alert email sent successfully on attempt {attempt + 1}")
                    return True
                    
                except Exception as e:
                    logger.warning(f"Email send attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    else:
                        logger.error(f"All email send attempts failed: {e}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error creating/sending email: {e}")
            return False


# Global email alert service instance
email_alert_service = EmailAlertService()
