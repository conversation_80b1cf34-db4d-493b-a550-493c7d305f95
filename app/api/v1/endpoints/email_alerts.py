"""
Email Alert Testing Endpoints
Endpoints for testing and managing the email alert system
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

from app.core.security.auth import get_current_user
from app.schemas.user import UserResponse
from app.core.app_rules import ApiResponse, success_response, error_response
from app.services.email_alert_service import email_alert_service, AlertType, AlertLevel
from app.utils.email_alert import (
    send_error_alert,
    send_database_error_alert,
    send_api_error_alert,
    send_authentication_error_alert,
    send_external_service_error_alert,
    send_performance_alert,
    send_custom_alert
)

router = APIRouter()


class TestAlertRequest(BaseModel):
    """Request model for testing alerts"""
    title: str = Field(..., description="Alert title", example="Test Alert")
    description: str = Field(..., description="Alert description", example="This is a test alert")
    alert_type: AlertType = Field(default=AlertType.CUSTOM, description="Type of alert")
    additional_data: Optional[Dict[str, Any]] = Field(default=None, description="Additional context data")


class AlertStatusResponse(BaseModel):
    """Response model for alert status"""
    enabled: bool = Field(..., description="Whether email alerts are enabled")
    sender_email: str = Field(..., description="Sender email address")
    recipient_email: str = Field(..., description="Recipient email address")
    smtp_server: str = Field(..., description="SMTP server")
    smtp_port: int = Field(..., description="SMTP port")


@router.get(
    "/email-alerts/status",
    response_model=ApiResponse,
    summary="Get Email Alert Status",
    description="Get the current status and configuration of the email alert system"
)
async def get_alert_status(
    current_user: UserResponse = Depends(get_current_user)
):
    """Get email alert system status"""
    try:
        status_data = AlertStatusResponse(
            enabled=email_alert_service.enabled,
            sender_email=email_alert_service.sender_email,
            recipient_email=email_alert_service.recipient_email,
            smtp_server=email_alert_service.smtp_server,
            smtp_port=email_alert_service.smtp_port
        )
        
        return success_response(
            details=status_data.model_dump(),
            title="Alert Status Retrieved",
            description="Email alert system status retrieved successfully"
        )
        
    except Exception as e:
        return error_response(
            error_code=500,
            title="Status Retrieval Failed",
            description=f"Failed to retrieve alert status: {str(e)}"
        )


@router.post(
    "/email-alerts/test",
    response_model=ApiResponse,
    summary="Test Email Alert",
    description="Send a test email alert to verify the system is working"
)
async def test_email_alert(
    current_user: UserResponse = Depends(get_current_user)
):
    """Send a test email alert"""
    try:
        success = await email_alert_service.send_critical_alert(
            title="Test Alert from GrowthHive",
            description="This is a test alert to verify the email alert system is working correctly.",
            alert_type=AlertType.CUSTOM,
            additional_data={
                "test_user": current_user.email,
                "test_timestamp": "2024-01-01T00:00:00Z",
                "system_status": "operational"
            }
        )
        
        if success:
            return success_response(
                details={"alert_sent": True, "message": "Test alert sent successfully"},
                title="Test Alert Sent",
                description="Test email alert has been sent successfully"
            )
        else:
            return error_response(
                error_code=500,
                title="Test Alert Failed",
                description="Failed to send test email alert"
            )
            
    except Exception as e:
        return error_response(
            error_code=500,
            title="Test Alert Error",
            description=f"Error sending test alert: {str(e)}"
        )


@router.post(
    "/email-alerts/test-error",
    response_model=ApiResponse,
    summary="Test Error Alert",
    description="Trigger a test error to verify exception handling and alerts"
)
async def test_error_alert(
    current_user: UserResponse = Depends(get_current_user)
):
    """Trigger a test error for alert testing"""
    try:
        # This will trigger the global exception handler and send an alert
        raise ValueError("This is a test error for email alert verification")
        
    except ValueError as e:
        # Send a custom alert for this test
        await send_error_alert(
            title="Test Error Alert",
            description="This is a test error alert triggered manually",
            exception=e,
            additional_data={
                "test_user": current_user.email,
                "test_type": "manual_error_test"
            }
        )
        
        return success_response(
            details={"error_triggered": True, "alert_sent": True},
            title="Test Error Triggered",
            description="Test error has been triggered and alert sent"
        )


@router.post(
    "/email-alerts/custom",
    response_model=ApiResponse,
    summary="Send Custom Alert",
    description="Send a custom alert with specified parameters"
)
async def send_custom_alert_endpoint(
    request: TestAlertRequest,
    current_user: UserResponse = Depends(get_current_user)
):
    """Send a custom alert"""
    try:
        # Add user context to additional data
        additional_data = request.additional_data or {}
        additional_data.update({
            "triggered_by": current_user.email,
            "user_id": str(current_user.id)
        })
        
        success = await email_alert_service.send_critical_alert(
            title=request.title,
            description=request.description,
            alert_type=request.alert_type,
            additional_data=additional_data
        )
        
        if success:
            return success_response(
                details={
                    "alert_sent": True,
                    "title": request.title,
                    "alert_type": request.alert_type.value
                },
                title="Custom Alert Sent",
                description="Custom alert has been sent successfully"
            )
        else:
            return error_response(
                error_code=500,
                title="Custom Alert Failed",
                description="Failed to send custom alert"
            )
            
    except Exception as e:
        return error_response(
            error_code=500,
            title="Custom Alert Error",
            description=f"Error sending custom alert: {str(e)}"
        )


@router.post(
    "/email-alerts/test-database-error",
    response_model=ApiResponse,
    summary="Test Database Error Alert",
    description="Send a test database error alert"
)
async def test_database_error_alert(
    current_user: UserResponse = Depends(get_current_user)
):
    """Send a test database error alert"""
    try:
        success = await send_database_error_alert(
            operation="SELECT",
            error_message="Connection timeout while querying users table",
            table_name="users",
            query="SELECT * FROM users WHERE id = ?",
            exception=Exception("Database connection timeout")
        )
        
        if success:
            return success_response(
                details={"alert_sent": True, "alert_type": "database_error"},
                title="Database Error Alert Sent",
                description="Test database error alert has been sent successfully"
            )
        else:
            return error_response(
                error_code=500,
                title="Database Alert Failed",
                description="Failed to send database error alert"
            )
            
    except Exception as e:
        return error_response(
            error_code=500,
            title="Database Alert Error",
            description=f"Error sending database alert: {str(e)}"
        )


@router.post(
    "/email-alerts/test-security-alert",
    response_model=ApiResponse,
    summary="Test Security Alert",
    description="Send a test security alert"
)
async def test_security_alert(
    current_user: UserResponse = Depends(get_current_user)
):
    """Send a test security alert"""
    try:
        success = await email_alert_service.send_security_alert(
            event="Suspicious Login Attempt",
            description="Multiple failed login attempts detected from unusual location",
            user_id=str(current_user.id),
            ip_address="*************",
            additional_data={
                "failed_attempts": 5,
                "location": "Unknown",
                "user_agent": "Test Browser"
            }
        )
        
        if success:
            return success_response(
                details={"alert_sent": True, "alert_type": "security"},
                title="Security Alert Sent",
                description="Test security alert has been sent successfully"
            )
        else:
            return error_response(
                error_code=500,
                title="Security Alert Failed",
                description="Failed to send security alert"
            )
            
    except Exception as e:
        return error_response(
            error_code=500,
            title="Security Alert Error",
            description=f"Error sending security alert: {str(e)}"
        )
