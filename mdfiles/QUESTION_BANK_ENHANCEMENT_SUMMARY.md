# 📋 **Question Bank APIs Enhancement - Lead & Franchisor Names**

## 🎯 **REQUEST SUMMARY**
Add lead name and franchisor name to the question bank and escalation question bank API responses for endpoints:
```
GET /api/questions_Module/question-bank
GET /api/questions_Module/escalation-question-bank
```

## ✅ **IMPLEMENTATION COMPLETED**

### 📊 **Enhanced API Response Structure**

#### **Question Bank API Response:**
```json
{
  "success": true,
  "message": "Retrieved X question bank items",
  "title": "Question Bank Retrieved",
  "data": {
    "items": [
      {
        "id": "uuid",
        "name": "What is your investment budget?",
        "lead_id": "uuid",
        "lead_first_name": "<PERSON>",
        "lead_last_name": "<PERSON><PERSON>",
        "franchisor_id": "uuid",
        "franchisor_name": "Coochie Hydrogreen",
        "is_deleted": false,
        "is_active": true,
        "created_at": "2024-01-01T12:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "items_per_page": 20,
      "total_items": 100,
      "total_pages": 5
    }
  }
}
```

#### **Escalation Question Bank API Response:**
```json
{
  "success": true,
  "message": "Retrieved X escalation question bank items",
  "title": "Escalation Question Bank Retrieved",
  "data": {
    "items": [
      {
        "id": "uuid",
        "name": "Budget inquiry escalation",
        "lead_id": "uuid",
        "lead_first_name": "Jane",
        "lead_last_name": "Smith",
        "franchisor_id": "uuid",
        "franchisor_name": "Coochie Hydrogreen",
        "answer": ["$50,000-$100,000", "$100,000-$200,000", "$200,000+"],
        "support_status": "Pending review",
        "is_deleted": false,
        "is_active": true,
        "created_at": "2024-01-01T12:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "items_per_page": 20,
      "total_items": 50,
      "total_pages": 3
    }
  }
}
```

---

## 🔧 **CHANGES IMPLEMENTED**

### **1. Schema Layer Updates**
**File:** `app/schemas/prequalification.py`

#### **Enhanced QuestionBankResponse:**
```python
class QuestionBankResponse(BaseModel):
    id: str = Field(..., description="Question bank ID")
    name: str = Field(..., description="Question name")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    lead_first_name: Optional[str] = Field(None, description="First name of the associated lead")
    lead_last_name: Optional[str] = Field(None, description="Last name of the associated lead")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    franchisor_name: Optional[str] = Field(None, description="Name of the associated franchisor")
    # ... other existing fields
```

#### **Enhanced EscalationQuestionBankResponse:**
```python
class EscalationQuestionBankResponse(BaseModel):
    id: str = Field(..., description="Escalation question bank ID")
    name: str = Field(..., description="Question name")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    lead_first_name: Optional[str] = Field(None, description="First name of the associated lead")
    lead_last_name: Optional[str] = Field(None, description="Last name of the associated lead")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    franchisor_name: Optional[str] = Field(None, description="Name of the associated franchisor")
    # ... other existing fields
```

### **2. API Endpoint Updates**
**File:** `app/api/v1/endpoints/prequalification.py`

#### **Added Model Imports:**
```python
from app.models.lead import Lead
from app.models.franchisor import Franchisor
```

#### **Enhanced Question Bank Endpoint:**
- **Efficient Batch Fetching**: Collects unique lead and franchisor IDs
- **Caching Strategy**: Creates lookup caches for leads and franchisors
- **Single Query Per Type**: One query for all leads, one for all franchisors

```python
# Get unique lead and franchisor IDs
lead_ids = {item.lead_id for item in question_bank_items if item.lead_id}
franchisor_ids = {item.franchisor_id for item in question_bank_items if item.franchisor_id}

# Fetch lead information
lead_cache = {}
if lead_ids:
    lead_query = select(Lead).where(Lead.id.in_(lead_ids))
    lead_result = await db.execute(lead_query)
    leads = lead_result.scalars().all()
    for lead in leads:
        lead_cache[lead.id] = {
            'first_name': lead.first_name,
            'last_name': lead.last_name
        }

# Fetch franchisor information
franchisor_cache = {}
if franchisor_ids:
    franchisor_query = select(Franchisor).where(Franchisor.id.in_(franchisor_ids))
    franchisor_result = await db.execute(franchisor_query)
    franchisors = franchisor_result.scalars().all()
    for franchisor in franchisors:
        franchisor_cache[franchisor.id] = franchisor.name
```

#### **Enhanced Individual Response Creation:**
- **Update Answer Endpoint**: Includes lead and franchisor information
- **Update Status Endpoint**: Includes lead and franchisor information
- **Single Item Queries**: Fetches related data for individual operations

---

## 🎯 **AFFECTED ENDPOINTS**

### **1. Question Bank List**
```
GET /api/questions_Module/question-bank
```
- ✅ Now includes `lead_first_name`, `lead_last_name`, and `franchisor_name`
- ✅ Efficient batch fetching for performance
- ✅ Maintains all existing functionality

### **2. Escalation Question Bank List**
```
GET /api/questions_Module/escalation-question-bank
```
- ✅ Now includes `lead_first_name`, `lead_last_name`, and `franchisor_name`
- ✅ Efficient batch fetching for performance
- ✅ Maintains all existing functionality

### **3. Update Escalation Answer**
```
PUT /api/questions_Module/escalation-question-bank/{escalation_id}/answer
```
- ✅ Response now includes lead and franchisor names
- ✅ Individual queries for single item operations

### **4. Update Escalation Status**
```
PUT /api/questions_Module/escalation-question-bank/{escalation_id}/status
```
- ✅ Response now includes lead and franchisor names
- ✅ Individual queries for single item operations

---

## ✅ **REQUIREMENTS COMPLIANCE**

### **✅ Application Conventions**
- Follows existing `StandardResponse` pattern
- Maintains consistent error handling
- Uses proper async/await patterns

### **✅ Authentication**
- All endpoints remain authenticated
- Uses `get_current_user` dependency

### **✅ Required Fields**
- All existing fields preserved
- Added new optional fields: `lead_first_name`, `lead_last_name`, `franchisor_name`

### **✅ No Breaking Changes**
- Existing functionality preserved
- New fields are optional (nullable)
- Backward compatible

### **✅ Performance Optimization**
- **Batch Fetching**: Single query per entity type for list endpoints
- **Caching Strategy**: Efficient lookup caches to avoid N+1 queries
- **Conditional Queries**: Only fetch data when IDs are present

---

## 🧪 **TESTING STATUS**

### **✅ Schema Validation**
- New fields validate correctly
- Pydantic models work with enhanced structure
- No validation errors

### **✅ Import Validation**
- All model imports successful
- Schema instantiation working correctly
- No circular import issues

---

## 📊 **EXAMPLE USAGE**

### **Request:**
```bash
GET /api/questions_Module/question-bank?page=1&limit=20
Authorization: Bearer <token>
```

### **Enhanced Response:**
```json
{
  "success": true,
  "message": "Retrieved 15 question bank items",
  "title": "Question Bank Retrieved",
  "data": {
    "items": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "What is your investment budget?",
        "lead_id": "123e4567-e89b-12d3-a456-************",
        "lead_first_name": "John",
        "lead_last_name": "Doe",
        "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
        "franchisor_name": "Coochie Hydrogreen",
        "is_deleted": false,
        "is_active": true,
        "created_at": "2024-01-01T12:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "items_per_page": 20,
      "total_items": 15,
      "total_pages": 1
    }
  }
}
```

---

## 🎯 **SUMMARY**

✅ **Lead and franchisor names successfully added to question bank APIs**  
✅ **Both `lead_first_name`, `lead_last_name`, and `franchisor_name` included**  
✅ **All existing functionality preserved**  
✅ **Performance optimized with batch fetching**  
✅ **No breaking changes introduced**  
✅ **All endpoints enhanced consistently**  
✅ **Ready for production use**

---

*Enhancement completed on: 2025-07-29*  
*Status: ✅ COMPLETE - Lead and franchisor names now included in question bank APIs*
