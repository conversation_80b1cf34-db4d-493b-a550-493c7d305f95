"""
Email Alert Utilities
Convenient functions for sending various types of alerts
"""

import asyncio
from typing import Optional, Dict, Any
from app.services.email_alert_service import email_alert_service, AlertType, AlertLevel
from app.core.logging import logger


async def send_error_alert(
    title: str,
    description: str,
    exception: Optional[Exception] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Send a general error alert
    
    Args:
        title: Error title
        description: Error description
        exception: Exception object if applicable
        additional_data: Additional context data
        
    Returns:
        bool: True if sent successfully
    """
    return await email_alert_service.send_critical_alert(
        title=title,
        description=description,
        alert_type=AlertType.SYSTEM_ERROR,
        additional_data=additional_data,
        exception=exception
    )


async def send_database_error_alert(
    operation: str,
    error_message: str,
    table_name: Optional[str] = None,
    query: Optional[str] = None,
    exception: Optional[Exception] = None
) -> bool:
    """
    Send a database-specific error alert
    
    Args:
        operation: Database operation that failed
        error_message: Error message
        table_name: Table name if applicable
        query: SQL query if applicable
        exception: Exception object if applicable
        
    Returns:
        bool: True if sent successfully
    """
    additional_data = {
        "operation": operation,
        "error_message": error_message
    }
    
    if table_name:
        additional_data["table_name"] = table_name
    if query:
        additional_data["query"] = query
        
    return await email_alert_service.send_critical_alert(
        title=f"Database Error: {operation}",
        description=error_message,
        alert_type=AlertType.DATABASE_ERROR,
        additional_data=additional_data,
        exception=exception
    )


async def send_api_error_alert(
    endpoint: str,
    method: str,
    status_code: int,
    error_message: str,
    user_id: Optional[str] = None,
    exception: Optional[Exception] = None
) -> bool:
    """
    Send an API-specific error alert
    
    Args:
        endpoint: API endpoint that failed
        method: HTTP method
        status_code: HTTP status code
        error_message: Error message
        user_id: User ID if applicable
        exception: Exception object if applicable
        
    Returns:
        bool: True if sent successfully
    """
    additional_data = {
        "endpoint": endpoint,
        "method": method,
        "status_code": status_code,
        "error_message": error_message
    }
    
    if user_id:
        additional_data["user_id"] = user_id
        
    return await email_alert_service.send_critical_alert(
        title=f"API Error: {method} {endpoint}",
        description=error_message,
        alert_type=AlertType.API_ERROR,
        additional_data=additional_data,
        exception=exception
    )


async def send_authentication_error_alert(
    event: str,
    user_email: Optional[str] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    additional_context: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Send an authentication-related error alert
    
    Args:
        event: Authentication event description
        user_email: User email if applicable
        ip_address: IP address
        user_agent: User agent string
        additional_context: Additional context data
        
    Returns:
        bool: True if sent successfully
    """
    context_data = {
        "event": event
    }
    
    if user_email:
        context_data["user_email"] = user_email
    if ip_address:
        context_data["ip_address"] = ip_address
    if user_agent:
        context_data["user_agent"] = user_agent
    if additional_context:
        context_data.update(additional_context)
        
    return await email_alert_service.send_security_alert(
        event=event,
        description=f"Authentication event: {event}",
        additional_data=context_data
    )


async def send_external_service_error_alert(
    service_name: str,
    operation: str,
    error_message: str,
    response_code: Optional[int] = None,
    exception: Optional[Exception] = None
) -> bool:
    """
    Send an external service error alert
    
    Args:
        service_name: Name of the external service
        operation: Operation that failed
        error_message: Error message
        response_code: HTTP response code if applicable
        exception: Exception object if applicable
        
    Returns:
        bool: True if sent successfully
    """
    additional_data = {
        "service_name": service_name,
        "operation": operation,
        "error_message": error_message
    }
    
    if response_code:
        additional_data["response_code"] = response_code
        
    return await email_alert_service.send_critical_alert(
        title=f"External Service Error: {service_name}",
        description=f"Operation '{operation}' failed: {error_message}",
        alert_type=AlertType.EXTERNAL_SERVICE_ERROR,
        additional_data=additional_data,
        exception=exception
    )


async def send_performance_alert(
    metric_name: str,
    current_value: float,
    threshold: float,
    unit: str = "",
    additional_metrics: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Send a performance-related alert
    
    Args:
        metric_name: Name of the performance metric
        current_value: Current value of the metric
        threshold: Threshold that was exceeded
        unit: Unit of measurement
        additional_metrics: Additional performance metrics
        
    Returns:
        bool: True if sent successfully
    """
    additional_data = {
        "metric_name": metric_name,
        "current_value": current_value,
        "threshold": threshold,
        "unit": unit
    }
    
    if additional_metrics:
        additional_data.update(additional_metrics)
        
    description = f"Performance metric '{metric_name}' exceeded threshold. Current: {current_value}{unit}, Threshold: {threshold}{unit}"
    
    return await email_alert_service.send_critical_alert(
        title=f"Performance Alert: {metric_name}",
        description=description,
        alert_type=AlertType.PERFORMANCE_ALERT,
        additional_data=additional_data
    )


async def send_custom_alert(
    title: str,
    description: str,
    level: AlertLevel = AlertLevel.INFO,
    additional_data: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Send a custom alert
    
    Args:
        title: Alert title
        description: Alert description
        level: Alert level
        additional_data: Additional context data
        
    Returns:
        bool: True if sent successfully
    """
    # Map alert level to alert type
    alert_type = AlertType.CUSTOM
    if level in [AlertLevel.CRITICAL, AlertLevel.ERROR]:
        alert_type = AlertType.SYSTEM_ERROR
        
    return await email_alert_service.send_critical_alert(
        title=title,
        description=description,
        alert_type=alert_type,
        additional_data=additional_data
    )


# Decorator for automatic error alerting
def alert_on_error(
    alert_title: Optional[str] = None,
    include_args: bool = False,
    alert_type: AlertType = AlertType.SYSTEM_ERROR
):
    """
    Decorator to automatically send alerts when a function raises an exception
    
    Args:
        alert_title: Custom alert title (defaults to function name)
        include_args: Whether to include function arguments in alert
        alert_type: Type of alert to send
    """
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                title = alert_title or f"Error in {func.__name__}"
                additional_data = {
                    "function_name": func.__name__,
                    "module": func.__module__
                }
                
                if include_args:
                    additional_data["args"] = str(args)
                    additional_data["kwargs"] = str(kwargs)
                
                await email_alert_service.send_critical_alert(
                    title=title,
                    description=str(e),
                    alert_type=alert_type,
                    additional_data=additional_data,
                    exception=e
                )
                raise
                
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # For sync functions, we can't await, so we'll log instead
                logger.error(f"Error in {func.__name__}: {e}")
                raise
                
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator
