"""add_conversation_message_table

Revision ID: f752351b0351
Revises: 5516b4a1ea04
Create Date: 2025-07-29 14:52:16.132921

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = 'f752351b0351'
down_revision: Union[str, None] = '5516b4a1ea04'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create conversation_message table"""

    # Create conversation_message table
    op.create_table(
        'conversation_message',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.func.gen_random_uuid()),
        sa.Column('lead_id', UUID(as_uuid=True), sa.ForeignKey('leads.id'), nullable=False, comment="Reference to the lead who is part of this conversation"),
        sa.Column('franchisor_id', UUID(as_uuid=True), sa.<PERSON>ey('franchisors.id'), nullable=True, comment="Reference to the franchisor associated with this conversation"),
        sa.Column('sender', sa.String(10), nullable=False, comment="Who sent the message: 'lead' or 'system'"),
        sa.Column('message', sa.Text(), nullable=False, comment="The actual message content"),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False, comment="Whether this message is active"),
        sa.Column('is_deleted', sa.Boolean(), default=False, nullable=False, comment="Whether this message has been soft deleted"),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False, comment="When this message was created"),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False, comment="When this message was last updated"),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, comment="When this message was soft deleted"),

        # Add constraints
        sa.CheckConstraint("sender IN ('lead', 'system')", name='check_sender_valid'),
        sa.CheckConstraint("message IS NOT NULL AND LENGTH(TRIM(message)) > 0", name='check_message_not_empty'),
    )

    # Create indexes
    op.create_index('ix_conversation_message_lead_id', 'conversation_message', ['lead_id'])
    op.create_index('ix_conversation_message_franchisor_id', 'conversation_message', ['franchisor_id'])
    op.create_index('ix_conversation_message_sender', 'conversation_message', ['sender'])
    op.create_index('ix_conversation_message_is_active', 'conversation_message', ['is_active'])
    op.create_index('ix_conversation_message_is_deleted', 'conversation_message', ['is_deleted'])
    op.create_index('ix_conversation_message_created_at', 'conversation_message', ['created_at'])


def downgrade() -> None:
    """Drop conversation_message table"""

    # Drop indexes
    op.drop_index('ix_conversation_message_created_at', table_name='conversation_message')
    op.drop_index('ix_conversation_message_is_deleted', table_name='conversation_message')
    op.drop_index('ix_conversation_message_is_active', table_name='conversation_message')
    op.drop_index('ix_conversation_message_sender', table_name='conversation_message')
    op.drop_index('ix_conversation_message_franchisor_id', table_name='conversation_message')
    op.drop_index('ix_conversation_message_lead_id', table_name='conversation_message')

    # Drop table
    op.drop_table('conversation_message')
