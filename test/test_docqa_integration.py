#!/usr/bin/env python3
"""
Test script for DocQA integration with GrowthHive API
"""

import requests
import time

# Configuration
BASE_URL = "http://localhost:8000/api"
LOGIN_EMAIL = "<EMAIL>"
LOGIN_PASSWORD = "Admin@1234"

class DocQAIntegrationTester:
    """Test class for DocQA integration functionality"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.token = None
        self.headers = {}
    
    def login(self) -> bool:
        """Login and get authentication token"""
        print("🔐 Logging in...")
        
        login_data = {
            "email_or_mobile": LOGIN_EMAIL,
            "password": LOGIN_PASSWORD,
            "remember_me": True
        }
        
        response = requests.post(f"{self.base_url}/auth/login", json=login_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                self.token = result["data"]["details"]["access_token"]
                self.headers = {"Authorization": f"Bearer {self.token}"}
                print("✅ Login successful!")
                return True
        
        print(f"❌ Login failed: {response.text}")
        return False
    
    def test_docqa_health(self) -> bool:
        """Test DocQA system health"""
        print("\n🏥 Testing DocQA health...")
        
        response = requests.get(f"{self.base_url}/docqa/health", headers=self.headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ DocQA Health: {result['data']['status']}")
            print(f"📊 Configuration: {result['data']['config']}")
            return True
        else:
            print(f"❌ Health check failed: {response.text}")
            return False
    
    def upload_test_document(self) -> str:
        """Upload a test document and return document ID"""
        print("\n📄 Uploading test document...")
        
        # Create a simple test file
        test_content = """
        FRANCHISE OPPORTUNITY - COFFEE MASTERS
        
        Investment Details:
        - Initial Investment: $80,000 - $150,000
        - Franchise Fee: $45,000
        - Royalty: 6% of gross sales
        - Marketing Fee: 2% of gross sales
        
        What We Provide:
        - 3 weeks comprehensive training
        - Site selection assistance
        - Equipment package
        - Ongoing operational support
        - Marketing materials and campaigns
        
        Contact Information:
        Phone: (03) 9876-5432
        Email: <EMAIL>
        Website: www.coffeemasters.com.au/franchise
        """
        
        files = {
            'file': ('test_franchise_doc.txt', test_content, 'text/plain')
        }
        
        data = {
            'name': 'Test Franchise Document',
            'description': 'Test document for DocQA integration',
            'is_active': True
        }
        
        response = requests.post(
            f"{self.base_url}/documents/upload",
            files=files,
            data=data,
            headers=self.headers
        )
        
        if response.status_code == 201:
            result = response.json()
            document_id = result["data"]["id"]
            print(f"✅ Document uploaded successfully! ID: {document_id}")
            print("⏳ DocQA processing initiated automatically...")
            return document_id
        else:
            print(f"❌ Document upload failed: {response.text}")
            return None
    
    def test_document_question(self, document_id: str) -> bool:
        """Test asking a question about a specific document"""
        print(f"\n❓ Testing document-specific question for ID: {document_id}")
        
        # Wait a bit for processing to complete
        print("⏳ Waiting 10 seconds for DocQA processing to complete...")
        time.sleep(10)
        
        question_data = {
            "question": "What is the franchise fee for Coffee Masters?",
            "top_k": 5,
            "similarity_threshold": 0.6
        }
        
        response = requests.post(
            f"{self.base_url}/docqa/ask/document/{document_id}",
            json=question_data,
            headers=self.headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Question answered successfully!")
            print(f"📝 Question: {result['data']['question']}")
            print(f"💬 Answer: {result['data']['answer']}")
            print(f"📄 Document: {result['data']['document_name']}")
            return True
        else:
            print(f"❌ Question failed: {response.text}")
            return False
    
    def test_general_question(self) -> bool:
        """Test asking a general question"""
        print("\n❓ Testing general question...")
        
        question_data = {
            "question": "What franchise opportunities are available?",
            "top_k": 6,
            "similarity_threshold": 0.7
        }
        
        response = requests.post(
            f"{self.base_url}/docqa/ask",
            json=question_data,
            headers=self.headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ General question answered successfully!")
            print(f"📝 Question: {result['data']['question']}")
            print(f"💬 Answer: {result['data']['answer'][:200]}...")
            return True
        else:
            print(f"❌ General question failed: {response.text}")
            return False
    
    def test_manual_processing(self, document_id: str) -> bool:
        """Test manual document processing"""
        print(f"\n🔄 Testing manual processing for document ID: {document_id}")
        
        response = requests.post(
            f"{self.base_url}/docqa/process/document/{document_id}",
            headers=self.headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Manual processing successful!")
            print(f"📊 Chunks created: {result['data']['chunks_created']}")
            print(f"🗄️ Table: {result['data']['table_name']}")
            return True
        else:
            print(f"❌ Manual processing failed: {response.text}")
            return False
    
    def run_full_test(self):
        """Run complete integration test"""
        print("🚀 Starting DocQA Integration Test")
        print("=" * 50)
        
        # Step 1: Login
        if not self.login():
            return False
        
        # Step 2: Test DocQA health
        if not self.test_docqa_health():
            return False
        
        # Step 3: Upload test document (automatic processing)
        document_id = self.upload_test_document()
        if not document_id:
            return False
        
        # Step 4: Test document-specific question
        if not self.test_document_question(document_id):
            return False
        
        # Step 5: Test general question
        if not self.test_general_question():
            return False
        
        # Step 6: Test manual processing
        if not self.test_manual_processing(document_id):
            return False
        
        print("\n🎉 All tests completed successfully!")
        print("\n📋 Summary:")
        print("✅ Automatic DocQA processing on upload")
        print("✅ Document-specific question answering")
        print("✅ General question answering")
        print("✅ Manual processing trigger")
        print("✅ Health monitoring")
        
        return True


def main():
    """Main test function"""
    tester = DocQAIntegrationTester()
    
    print("📚 DocQA Integration Test Suite")
    print("This script tests the automatic DocQA processing and testing functionality")
    print()
    
    success = tester.run_full_test()
    
    if success:
        print("\n🎯 Integration test completed successfully!")
        print("\n📖 How to use:")
        print("1. Upload documents via /api/documents/upload - automatic processing")
        print("2. Upload brochures via /api/franchisors/{id}/upload-brochure - automatic processing")
        print("3. Test with specific document: POST /api/docqa/ask/document/{document_id}")
        print("4. Test with specific franchisor: POST /api/docqa/ask/franchisor/{franchisor_id}")
        print("5. Ask general questions: POST /api/docqa/ask")
        print("6. Check system health: GET /api/docqa/health")
    else:
        print("\n❌ Integration test failed!")
        print("Please check the API server and DocQA configuration.")


if __name__ == "__main__":
    main()
