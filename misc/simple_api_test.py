#!/usr/bin/env python3
"""
Simple API test to verify franchisor changes work
"""

import asyncio
import httpx
import json
import uuid

BASE_URL = "http://localhost:8000"

async def test_api():
    async with httpx.AsyncClient() as client:
        # 1. Register a new user
        user_email = f"test_{str(uuid.uuid4())[:8]}@example.com"
        register_data = {
            "email": user_email,
            "password": "TestPassword123!",
            "confirm_password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User",
            "mobile": f"+{str(uuid.uuid4().int)[:10]}"
        }
        
        print("1. Registering user...")
        response = await client.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code == 201:
            print("✅ User registered successfully")
        else:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return
        
        # 2. Login
        print("2. Logging in...")
        login_data = {
            "email_or_mobile": user_email,
            "password": "TestPassword123!"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            print(f"Login response: {json.dumps(data, indent=2)}")
            # Try different possible locations for access token
            access_token = None
            if "data" in data and "access_token" in data["data"]:
                access_token = data["data"]["access_token"]
            elif "access_token" in data:
                access_token = data["access_token"]
            elif "data" in data and "details" in data["data"] and "access_token" in data["data"]["details"]:
                access_token = data["data"]["details"]["access_token"]

            if access_token:
                print("✅ Login successful")
            else:
                print("❌ Could not find access token in response")
                return
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return
        
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # 3. Test franchisor listing
        print("3. Testing franchisor listing...")
        response = await client.get(f"{BASE_URL}/api/franchisors/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            items = data.get("data", {}).get("items", [])
            print(f"✅ Franchisor listing works: {len(items)} items")
            
            # Check if franchisor_won_id field is present
            if items:
                first_item = items[0]
                if "franchisor_won_id" in first_item:
                    print(f"✅ franchisor_won_id field present: {first_item.get('franchisor_won_id')}")
                else:
                    print("❌ franchisor_won_id field missing")
        else:
            print(f"❌ Franchisor listing failed: {response.status_code} - {response.text}")
        
        # 4. Test sorting
        print("4. Testing sorting...")
        sort_tests = [
            {"sort_by": "name", "sort_order": "asc"},
            {"sort_by": "franchisor_won_id", "sort_order": "asc"},
            {"sort_by": "is_active", "sort_order": "desc"}
        ]
        
        for test in sort_tests:
            response = await client.get(
                f"{BASE_URL}/api/franchisors/",
                params=test,
                headers=headers
            )
            if response.status_code == 200:
                print(f"✅ Sort by {test['sort_by']} works")
            else:
                print(f"❌ Sort by {test['sort_by']} failed: {response.status_code}")
        
        # 5. Test Zoho sync endpoint
        print("5. Testing Zoho sync...")
        response = await client.post(f"{BASE_URL}/api/zoho/sync", headers=headers)
        if response.status_code == 200:
            print("✅ Zoho sync endpoint works")
        else:
            print(f"⚠️ Zoho sync failed (expected if no Zoho config): {response.status_code}")
        
        print("\n✅ All basic tests completed!")

if __name__ == "__main__":
    asyncio.run(test_api())
