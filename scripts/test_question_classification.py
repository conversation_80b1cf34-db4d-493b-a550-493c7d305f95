"""
Tests for Question Classification System
Comprehensive tests for question classification agent, service, and API
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from app.agents.question_classification import QuestionClassificationAgent
from app.agents.base import Agent<PERSON>onfig, AgentRole
from app.services.question_classification_service import QuestionClassificationService
from app.schemas.question_classification import QuestionClassificationRequest
from app.models.lead_reference import QuestionBank, EscalationQuestionBank


class TestQuestionClassificationAgent:
    """Test the QuestionClassificationAgent"""
    
    @pytest.fixture
    def agent_config(self):
        """Create agent configuration for testing"""
        return AgentConfig(
            role=AgentRole.QUESTION_ANSWERING,
            model="gpt-4o",
            temperature=0.3,
            max_tokens=1000,
            description="Test classification agent"
        )
    
    @pytest.fixture
    def classification_agent(self, agent_config):
        """Create classification agent for testing"""
        return QuestionClassificationAgent(agent_config)
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, classification_agent):
        """Test that the agent initializes correctly"""
        assert classification_agent is not None
        assert len(classification_agent.tools) > 0
        
        # Check that required tools are available
        tool_names = [tool.name for tool in classification_agent.tools]
        assert "insert_question_bank" in tool_names
        assert "insert_escalation_question_bank" in tool_names
        assert "analyze_question" in tool_names
    
    @pytest.mark.asyncio
    @patch('docqa.central_api.ask_question')
    async def test_rag_answer_attempt(self, mock_ask_question, classification_agent):
        """Test RAG answer attempt"""
        # Mock RAG response
        mock_ask_question.return_value = {
            "answer": "This franchise has an ROI of 15-20% annually based on our financial projections."
        }
        
        result = await classification_agent._attempt_rag_answer(
            "What's the ROI of this franchise?",
            "test-franchisor-id"
        )
        
        assert "ROI of 15-20%" in result
        mock_ask_question.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_classify_answerable_question(self, classification_agent):
        """Test classification of an answerable question"""
        with patch.object(classification_agent, '_attempt_rag_answer') as mock_rag, \
             patch.object(classification_agent, '_analyze_question') as mock_analyze, \
             patch.object(classification_agent, '_classify_and_insert') as mock_insert:
            
            # Setup mocks
            mock_rag.return_value = "The ROI is 15-20% annually."
            mock_analyze.return_value = {
                "needs_escalation": False,
                "reason": "Question can be answered from available information"
            }
            mock_insert.return_value = {
                "classification": "answerable",
                "table": "question_bank",
                "sql_result": "INSERT INTO question_bank..."
            }
            
            result = await classification_agent.classify_question(
                question_text="What's the ROI of this franchise?",
                lead_id="test-lead-id",
                franchisor_id="test-franchisor-id"
            )
            
            assert result["success"] is True
            assert result["result"]["classification"] == "answerable"
    
    @pytest.mark.asyncio
    async def test_classify_escalation_question(self, classification_agent):
        """Test classification of an escalation question"""
        with patch.object(classification_agent, '_attempt_rag_answer') as mock_rag, \
             patch.object(classification_agent, '_analyze_question') as mock_analyze, \
             patch.object(classification_agent, '_classify_and_insert') as mock_insert:
            
            # Setup mocks
            mock_rag.return_value = "I don't have information about GST numbers."
            mock_analyze.return_value = {
                "needs_escalation": True,
                "reason": "Requires specific business information not in brochures"
            }
            mock_insert.return_value = {
                "classification": "escalation",
                "table": "escalation_question_bank",
                "sql_result": "INSERT INTO escalation_question_bank..."
            }
            
            result = await classification_agent.classify_question(
                question_text="What's your GST number?",
                lead_id="test-lead-id",
                franchisor_id="test-franchisor-id"
            )
            
            assert result["success"] is True
            assert result["result"]["classification"] == "escalation"


class TestQuestionClassificationService:
    """Test the QuestionClassificationService"""
    
    @pytest.fixture
    def mock_db(self):
        """Create mock database session"""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def classification_service(self, mock_db):
        """Create classification service for testing"""
        return QuestionClassificationService(mock_db)
    
    @pytest.mark.asyncio
    async def test_service_initialization(self, classification_service):
        """Test that the service initializes correctly"""
        assert classification_service is not None
        assert classification_service._agent is None  # Lazy initialization
    
    @pytest.mark.asyncio
    async def test_classify_valid_question(self, classification_service):
        """Test classifying a valid question"""
        with patch.object(classification_service, '_get_agent') as mock_get_agent:
            mock_agent = AsyncMock()
            mock_agent.classify_question.return_value = {
                "success": True,
                "result": {
                    "classification": "answerable",
                    "table": "question_bank",
                    "sql_result": "INSERT INTO question_bank...",
                    "answer": "The ROI is 15-20% annually."
                },
                "rag_answer": "The ROI is 15-20% annually.",
                "analysis": {"needs_escalation": False}
            }
            mock_get_agent.return_value = mock_agent
            
            result = await classification_service.classify_question(
                question_text="What's the ROI of this franchise?",
                lead_id="test-lead-id",
                franchisor_id="test-franchisor-id"
            )
            
            assert result["success"] is True
            assert result["data"]["classification"] == "answerable"
            assert "ROI is 15-20%" in result["data"]["answer"]
    
    @pytest.mark.asyncio
    async def test_classify_empty_question(self, classification_service):
        """Test classifying an empty question"""
        result = await classification_service.classify_question(
            question_text="",
            lead_id="test-lead-id"
        )
        
        assert result["success"] is False
        assert result["error_code"] == 400
        assert "required" in result["message"]["description"]
    
    @pytest.mark.asyncio
    async def test_batch_classification(self, classification_service):
        """Test batch question classification"""
        with patch.object(classification_service, 'classify_question') as mock_classify:
            # Mock individual classification results
            mock_classify.side_effect = [
                {
                    "success": True,
                    "data": {"classification": "answerable"}
                },
                {
                    "success": True,
                    "data": {"classification": "escalation"}
                }
            ]
            
            questions = [
                {
                    "question_text": "What's the ROI?",
                    "lead_id": "lead-1"
                },
                {
                    "question_text": "What's your GST number?",
                    "lead_id": "lead-2"
                }
            ]
            
            result = await classification_service.batch_classify_questions(questions)
            
            assert result["success"] is True
            assert result["data"]["total_questions"] == 2
            assert result["data"]["successful_classifications"] == 2
            assert result["data"]["failed_classifications"] == 0
    
    @pytest.mark.asyncio
    async def test_get_classification_examples(self, classification_service):
        """Test getting classification examples"""
        result = await classification_service.get_classification_examples()
        
        assert result["success"] is True
        assert "answerable_questions" in result["data"]
        assert "escalation_questions" in result["data"]
        assert "sql_examples" in result["data"]
        
        # Check that examples contain expected content
        answerable = result["data"]["answerable_questions"]
        assert len(answerable) > 0
        assert any("ROI" in q["question"] for q in answerable)
        
        escalation = result["data"]["escalation_questions"]
        assert len(escalation) > 0
        assert any("GST" in q["question"] for q in escalation)


class TestQuestionClassificationAPI:
    """Test the Question Classification API endpoints"""
    
    @pytest.mark.asyncio
    async def test_classify_endpoint_success(self):
        """Test successful question classification via API"""
        from app.api.v1.endpoints.question_classification import classify_question
        from app.schemas.question_classification import QuestionClassificationRequest
        
        # Mock dependencies
        mock_service = AsyncMock()
        mock_service.classify_question.return_value = {
            "success": True,
            "status": "success",
            "message": {
                "title": "Question Classified as Answerable",
                "description": "Question inserted into question_bank table"
            },
            "data": {
                "classification": "answerable",
                "table": "question_bank",
                "question": "What's the ROI?",
                "answer": "15-20% annually",
                "sql_statement": "INSERT INTO question_bank...",
                "analysis": {"needs_escalation": False}
            }
        }
        
        mock_user = MagicMock()
        
        request = QuestionClassificationRequest(
            question_text="What's the ROI of this franchise?",
            lead_id="test-lead-id",
            franchisor_id="test-franchisor-id"
        )
        
        result = await classify_question(request, mock_service, mock_user)
        
        assert result.success is True
        assert result.data.classification == "answerable"
        mock_service.classify_question.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_batch_classify_endpoint(self):
        """Test batch classification endpoint"""
        from app.api.v1.endpoints.question_classification import classify_questions_batch
        from app.schemas.question_classification import (
            BatchQuestionClassificationRequest,
            QuestionClassificationRequest
        )
        
        # Mock dependencies
        mock_service = AsyncMock()
        mock_service.batch_classify_questions.return_value = {
            "success": True,
            "status": "success",
            "message": {
                "title": "Batch Classification Complete",
                "description": "Processed 2 questions: 2 successful, 0 failed"
            },
            "data": {
                "total_questions": 2,
                "successful_classifications": 2,
                "failed_classifications": 0,
                "results": []
            }
        }
        
        mock_user = MagicMock()
        
        request = BatchQuestionClassificationRequest(
            questions=[
                QuestionClassificationRequest(question_text="What's the ROI?"),
                QuestionClassificationRequest(question_text="What's your GST number?")
            ]
        )
        
        result = await classify_questions_batch(request, mock_service, mock_user)
        
        assert result.success is True
        assert result.data.total_questions == 2
        mock_service.batch_classify_questions.assert_called_once()


class TestQuestionAnalysis:
    """Test question analysis logic"""
    
    def test_escalation_keywords(self):
        """Test that escalation keywords are detected"""
        escalation_questions = [
            "What's your GST number?",
            "I need immediate assistance",
            "This is urgent",
            "I want a refund",
            "I need legal advice"
        ]
        
        # These would be detected by the analyze_question tool
        for question in escalation_questions:
            assert any(keyword in question.lower() for keyword in [
                "gst number", "immediate assistance", "urgent", "refund", "legal advice"
            ])
    
    def test_answerable_questions(self):
        """Test that answerable questions are properly identified"""
        answerable_questions = [
            "What's the ROI of this franchise?",
            "What are the training requirements?",
            "How much is the initial investment?",
            "What support do you provide?",
            "What are the ongoing fees?"
        ]
        
        # These should have sufficient length and clear intent
        for question in answerable_questions:
            assert len(question.split()) >= 3  # Minimum word count
            assert "?" in question  # Proper question format


if __name__ == "__main__":
    pytest.main([__file__])
