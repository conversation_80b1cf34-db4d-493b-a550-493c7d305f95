#!/usr/bin/env python3
"""
Comprehensive Test Script for PDF Upload and Ingestion
Tests the complete workflow from PDF upload to Q&A functionality
"""

import os
import sys
import requests
from pathlib import Path
import time

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Test configuration
BASE_URL = "http://localhost:8000"
PDF_FILE_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
TEST_FRANCHISOR_ID = "test_franchisor_123"

# Test credentials (you'll need to replace these with actual credentials)
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpassword123"

class PDFUploadTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.pdf_path = PDF_FILE_PATH
        self.access_token = None
        self.session_id = f"test_session_{int(time.time())}"
        
    def log(self, message, level="INFO"):
        """Log messages with timestamp"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def authenticate(self):
        """Authenticate and get access token"""
        try:
            self.log("🔐 Authenticating...")
            
            # Try to login (you may need to adjust this based on your auth system)
            login_data = {
                "username": TEST_EMAIL,
                "password": TEST_PASSWORD
            }
            
            response = requests.post(f"{self.base_url}/api/auth/login", json=login_data)
            
            if response.status_code == 200:
                result = response.json()
                self.access_token = result.get("access_token")
                self.log("✅ Authentication successful")
                return True
            else:
                self.log(f"❌ Authentication failed: {response.status_code} - {response.text}", "ERROR")
                # For testing, we'll continue without auth (if endpoints allow it)
                return False
                
        except Exception as e:
            self.log(f"❌ Authentication error: {str(e)}", "ERROR")
            return False
    
    def get_headers(self):
        """Get request headers with authentication"""
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    def test_server_health(self):
        """Test if the server is running"""
        try:
            self.log("🏥 Checking server health...")
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                self.log("✅ Server is healthy")
                return True
            else:
                self.log(f"⚠️ Server health check returned: {response.status_code}")
                return True  # Continue anyway
        except Exception as e:
            self.log(f"❌ Server health check failed: {str(e)}", "ERROR")
            return False
    
    def test_pdf_file_exists(self):
        """Check if the PDF file exists"""
        try:
            self.log("📄 Checking PDF file...")
            if os.path.exists(self.pdf_path):
                file_size = os.path.getsize(self.pdf_path)
                self.log(f"✅ PDF file found: {self.pdf_path} ({file_size:,} bytes)")
                return True
            else:
                self.log(f"❌ PDF file not found: {self.pdf_path}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ Error checking PDF file: {str(e)}", "ERROR")
            return False
    
    def test_agent_system_status(self):
        """Test agent system status"""
        try:
            self.log("🤖 Checking agent system status...")
            headers = self.get_headers()
            response = requests.get(f"{self.base_url}/api/agents/status", headers=headers)
            
            if response.status_code == 200:
                status = response.json()
                self.log("✅ Agent system is operational")
                self.log(f"   - Workflow compiled: {status.get('workflow_compiled', False)}")
                self.log(f"   - Checkpointer enabled: {status.get('checkpointer_enabled', False)}")
                self.log(f"   - Number of agents: {len(status.get('agents', {}))}")
                return True
            else:
                self.log(f"⚠️ Agent system status: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Error checking agent system: {str(e)}", "ERROR")
            return False
    
    def test_direct_agent_upload(self):
        """Test direct upload to agent system"""
        try:
            self.log("📤 Testing direct agent document upload...")
            
            with open(self.pdf_path, 'rb') as pdf_file:
                files = {
                    'file': ('Coochie_Information_pack.pdf', pdf_file, 'application/pdf')
                }
                data = {
                    'session_id': self.session_id,
                    'document_type': 'brochure'
                }
                
                headers = {}
                if self.access_token:
                    headers["Authorization"] = f"Bearer {self.access_token}"
                
                response = requests.post(
                    f"{self.base_url}/api/agents/upload-document",
                    files=files,
                    data=data,
                    headers=headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log("✅ Direct agent upload successful")
                    self.log(f"   - Session ID: {result.get('session_id')}")
                    self.log(f"   - Document ID: {result.get('document_id')}")
                    return True
                else:
                    self.log(f"❌ Direct agent upload failed: {response.status_code} - {response.text}", "ERROR")
                    return False
                    
        except Exception as e:
            self.log(f"❌ Error in direct agent upload: {str(e)}", "ERROR")
            return False
    
    def test_franchisor_upload(self):
        """Test upload via franchisor endpoint"""
        try:
            self.log("🏢 Testing franchisor brochure upload...")
            
            with open(self.pdf_path, 'rb') as pdf_file:
                files = {
                    'file': ('Coochie_Information_pack.pdf', pdf_file, 'application/pdf')
                }
                
                headers = {}
                if self.access_token:
                    headers["Authorization"] = f"Bearer {self.access_token}"
                
                response = requests.post(
                    f"{self.base_url}/api/franchisors/{TEST_FRANCHISOR_ID}/upload-brochure",
                    files=files,
                    headers=headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log("✅ Franchisor upload successful")
                    self.log(f"   - Message: {result.get('message', {}).get('description', 'Success')}")
                    return True
                else:
                    self.log(f"❌ Franchisor upload failed: {response.status_code} - {response.text}", "ERROR")
                    return False
                    
        except Exception as e:
            self.log(f"❌ Error in franchisor upload: {str(e)}", "ERROR")
            return False
    
    def test_chat_functionality(self):
        """Test chat functionality with the uploaded document"""
        try:
            self.log("💬 Testing chat functionality...")
            
            test_questions = [
                "Hello! I'm interested in franchise opportunities.",
                "What are the investment requirements for this franchise?",
                "Tell me about the franchise fees and costs.",
                "What support is provided to franchisees?",
                "What are the territory requirements?"
            ]
            
            headers = self.get_headers()
            
            for i, question in enumerate(test_questions, 1):
                self.log(f"   Question {i}: {question}")
                
                chat_data = {
                    "message": question,
                    "session_id": self.session_id
                }
                
                response = requests.post(
                    f"{self.base_url}/api/agents/chat",
                    json=chat_data,
                    headers=headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result.get('response', 'No response')
                    intent = result.get('intent', 'Unknown')
                    
                    self.log(f"   ✅ Response: {answer[:100]}...")
                    self.log(f"   📍 Intent: {intent}")
                    
                    # Small delay between questions
                    time.sleep(1)
                else:
                    self.log(f"   ❌ Chat failed: {response.status_code} - {response.text}", "ERROR")
                    return False
            
            self.log("✅ Chat functionality test completed")
            return True
            
        except Exception as e:
            self.log(f"❌ Error in chat functionality test: {str(e)}", "ERROR")
            return False
    
    def test_non_pdf_rejection(self):
        """Test that non-PDF files are rejected"""
        try:
            self.log("🚫 Testing non-PDF file rejection...")
            
            # Create a temporary text file
            temp_file_path = "/tmp/test_document.txt"
            with open(temp_file_path, 'w') as f:
                f.write("This is a test document that should be rejected.")
            
            try:
                with open(temp_file_path, 'rb') as test_file:
                    files = {
                        'file': ('test_document.txt', test_file, 'text/plain')
                    }
                    data = {
                        'session_id': self.session_id,
                        'document_type': 'brochure'
                    }
                    
                    headers = {}
                    if self.access_token:
                        headers["Authorization"] = f"Bearer {self.access_token}"
                    
                    response = requests.post(
                        f"{self.base_url}/api/agents/upload-document",
                        files=files,
                        data=data,
                        headers=headers
                    )
                    
                    if response.status_code == 400:
                        self.log("✅ Non-PDF file correctly rejected")
                        return True
                    else:
                        self.log(f"❌ Non-PDF file was not rejected: {response.status_code}", "ERROR")
                        return False
            finally:
                # Clean up temp file
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    
        except Exception as e:
            self.log(f"❌ Error in non-PDF rejection test: {str(e)}", "ERROR")
            return False
    
    def run_all_tests(self):
        """Run all tests in sequence"""
        self.log("🚀 Starting comprehensive PDF upload and ingestion tests...")
        self.log("=" * 60)
        
        tests = [
            ("Server Health Check", self.test_server_health),
            ("PDF File Existence", self.test_pdf_file_exists),
            ("Authentication", self.authenticate),
            ("Agent System Status", self.test_agent_system_status),
            ("Direct Agent Upload", self.test_direct_agent_upload),
            ("Franchisor Upload", self.test_franchisor_upload),
            ("Chat Functionality", self.test_chat_functionality),
            ("Non-PDF Rejection", self.test_non_pdf_rejection),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            self.log(f"\n🧪 Running: {test_name}")
            self.log("-" * 40)
            
            try:
                result = test_func()
                results[test_name] = result
                
                if result:
                    self.log(f"✅ {test_name}: PASSED")
                else:
                    self.log(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                self.log(f"💥 {test_name}: ERROR - {str(e)}", "ERROR")
                results[test_name] = False
        
        # Summary
        self.log("\n" + "=" * 60)
        self.log("📊 TEST SUMMARY")
        self.log("=" * 60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            self.log(f"{test_name}: {status}")
        
        self.log(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            self.log("🎉 All tests passed! PDF upload and ingestion system is working correctly.")
        else:
            self.log("⚠️ Some tests failed. Please check the logs above for details.")
        
        return passed == total


def main():
    """Main function to run the tests"""
    print("🤖 PDF Upload and Ingestion Integration Test")
    print("=" * 50)
    
    # Check if PDF file exists
    if not os.path.exists(PDF_FILE_PATH):
        print(f"❌ ERROR: PDF file not found at {PDF_FILE_PATH}")
        print("Please ensure the Coochie_Information pack.pdf file is in the correct location.")
        return False
    
    # Run tests
    tester = PDFUploadTester()
    success = tester.run_all_tests()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
