#!/bin/bash
"""
GrowthHive Health Check Script
Checks the status of all services
"""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🔍 $1${NC}"
}

# Header
echo "=================================================================="
print_header "GrowthHive Health Check"
echo "=================================================================="

# Check FastAPI Application
print_info "Checking FastAPI application..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    print_status "FastAPI application is running (http://localhost:8000)"
else
    print_error "FastAPI application is not responding"
fi

# Check API Documentation
print_info "Checking API documentation..."
if curl -s http://localhost:8000/api/docs > /dev/null 2>&1; then
    print_status "API documentation is accessible (http://localhost:8000/api/docs)"
else
    print_warning "API documentation may not be accessible"
fi

# Check RabbitMQ
print_info "Checking RabbitMQ..."
if docker exec growthhive-rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; then
    print_status "RabbitMQ is running (http://localhost:15672)"
else
    print_error "RabbitMQ is not responding"
fi

# Check Redis
print_info "Checking Redis..."
if docker exec growthhive-redis redis-cli ping > /dev/null 2>&1; then
    print_status "Redis is running"
else
    print_error "Redis is not responding"
fi

# Check Celery Worker
print_info "Checking Celery worker..."
if pgrep -f "celery_worker.py" > /dev/null; then
    CELERY_PID=$(pgrep -f "celery_worker.py")
    print_status "Celery worker is running (PID: $CELERY_PID)"
else
    print_error "Celery worker is not running"
fi

# Check Docker containers
print_info "Checking Docker containers..."
echo ""
docker-compose -f docker-compose.rabbitmq.yml ps
echo ""

# Check processes
print_info "Checking Python processes..."
PYTHON_PROCESSES=$(ps aux | grep -E "(uvicorn|celery_worker|start_server)" | grep -v grep)
if [ -n "$PYTHON_PROCESSES" ]; then
    echo "$PYTHON_PROCESSES"
else
    print_warning "No GrowthHive Python processes found"
fi

echo ""
print_header "Health Check Complete"
echo "=================================================================="
