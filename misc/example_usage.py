#!/usr/bin/env python3
"""
Example usage of the DocQA system
"""

import os
from docqa import ask_question
from docqa.serve import ask_question_stream, health_check, get_question_context

def main():
    """Demonstrate DocQA functionality"""
    
    print("🚀 DocQA System Example")
    print("=" * 50)
    
    # Check system health
    print("\n1. 🏥 Health Check")
    health = health_check()
    print(f"Status: {health['status']}")
    if health['status'] == 'healthy':
        print("✅ System is ready!")
    else:
        print(f"❌ System issue: {health.get('error')}")
        return
    
    # Example 1: Basic question answering
    print("\n2. 💬 Basic Question Answering")
    question = "What franchise opportunities are available in Melbourne?"
    print(f"Question: {question}")
    
    try:
        answer = ask_question(question)
        print(f"Answer: {answer}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Example 2: Question with metadata
    print("\n3. 📊 Question with Source Information")
    try:
        answer = ask_question(
            "What are the investment requirements?",
            include_metadata=True,
            top_k=5
        )
        print(f"Answer with sources: {answer}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Example 3: Streaming response
    print("\n4. 🌊 Streaming Response")
    print("Question: Tell me about the business model")
    print("Streaming answer: ", end="")
    
    try:
        for chunk in ask_question_stream("Tell me about the business model"):
            print(chunk, end="", flush=True)
        print()  # New line after streaming
    except Exception as e:
        print(f"\nError: {str(e)}")
    
    # Example 4: Get question context (debugging)
    print("\n5. 🔍 Question Context Analysis")
    try:
        context = get_question_context("What are the costs involved?", top_k=3)
        if 'error' not in context:
            print(f"Results found: {context['results_count']}")
            print(f"Franchisor results: {len(context.get('franchisor_results', []))}")
            print(f"Document results: {len(context.get('document_results', []))}")
        else:
            print(f"Context error: {context['error']}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Example 5: Document ingestion (if you have a test document)
    print("\n6. 📄 Document Ingestion Example")
    test_url = "https://example.com/test-document.pdf"  # Replace with actual URL
    
    print("Note: To ingest a document, use:")
    print("  from docqa.ingest import DocumentIngestionService")
    print("  service = DocumentIngestionService()")
    print(f"  result = service.ingest_document('{test_url}')")
    print("  print(f'Success: {result.success}, Chunks: {result.chunks_created}')")
    
    # Example 6: JSON format response
    print("\n7. 📋 JSON Format Response")
    try:
        json_answer = ask_question(
            "What support is provided to franchisees?",
            format="json",
            include_metadata=True
        )
        print("JSON Response:")
        print(json_answer)
    except Exception as e:
        print(f"Error: {str(e)}")
    
    print("\n✨ Example completed!")
    print("\nTo use the CLI:")
    print("  python -m docqa.cli ask 'Your question here'")
    print("  python -m docqa.cli ingest s3://bucket/document.pdf")
    print("  python -m docqa.cli interactive")


if __name__ == "__main__":
    # Set up environment if not already set
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Please set OPENAI_API_KEY environment variable")
        print("   export OPENAI_API_KEY=sk-your-key-here")
        exit(1)
    
    if not os.getenv("DATABASE_URL"):
        print("⚠️  Please set DATABASE_URL environment variable")
        print("   export DATABASE_URL=postgresql://user:pass@localhost:5432/db")
        exit(1)
    
    main()
