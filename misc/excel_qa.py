#!/usr/bin/env python3
"""
Excel Q&A System - Process Excel files and answer questions about the data.
"""

import os

try:
    import pandas as pd
    import openai
    LIBS_AVAILABLE = True
except ImportError:
    LIBS_AVAILABLE = False


class ExcelQA:
    """Simple Excel Q&A system."""
    
    def __init__(self, api_key: str):
        """Initialize the Excel Q&A system."""
        self.api_key = api_key
        self.openai_client = openai.OpenAI(api_key=api_key)
        self.excel_data = None
        self.excel_summary = ""
        self.file_processed = False
    
    def process_excel_file(self, file_path: str) -> bool:
        """Process Excel file and prepare for Q&A."""
        try:
            print(f"📊 Processing Excel file: {file_path}")
            
            # Read all sheets
            self.excel_data = pd.read_excel(file_path, sheet_name=None)
            
            print("✅ Excel file loaded successfully!")
            print(f"📋 Total sheets: {len(self.excel_data)}")
            
            # Create comprehensive summary
            self.excel_summary = self._create_excel_summary()
            
            print("🧠 Excel data analysis completed")
            print("📄 Ready for questions!")
            
            self.file_processed = True
            return True
            
        except Exception as e:
            print(f"❌ Error processing Excel file: {e}")
            return False
    
    def _create_excel_summary(self) -> str:
        """Create a comprehensive summary of the Excel file."""
        summary_parts = [
            "=== EXCEL FILE ANALYSIS ===",
            f"Total Sheets: {len(self.excel_data)}",
            ""
        ]
        
        for sheet_name, df in self.excel_data.items():
            summary_parts.extend([
                f"SHEET: {sheet_name}",
                f"Dimensions: {len(df)} rows × {len(df.columns)} columns",
                f"Columns: {list(df.columns)}",
                ""
            ])
            
            # Add sample data
            if len(df) > 0:
                summary_parts.append("Sample Data:")
                try:
                    # Convert to string representation
                    sample_data = df.head(3).to_string()
                    summary_parts.append(sample_data)
                except:
                    summary_parts.append("(Data preview not available)")
                summary_parts.append("")
            
            # Add statistics for numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                summary_parts.append("Numeric Statistics:")
                try:
                    stats = df[numeric_cols].describe().to_string()
                    summary_parts.append(stats)
                except:
                    summary_parts.append("(Statistics not available)")
                summary_parts.append("")
        
        return "\n".join(summary_parts)
    
    def ask_question(self, question: str) -> str:
        """Ask a question about the Excel data."""
        if not self.file_processed:
            return "❌ No Excel file has been processed yet."
        
        try:
            # Create prompt with Excel data context
            prompt = f"""
Based on the following Excel file data, please answer the question accurately and comprehensively.

EXCEL FILE DATA:
{self.excel_summary}

QUESTION: {question}

Please provide a detailed answer based on the Excel data shown above. If the question asks for specific data, numbers, or analysis, extract the relevant information from the Excel sheets. If you need to perform calculations or analysis, do so based on the data provided.

ANSWER:"""
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert data analyst. Analyze Excel data and provide accurate, detailed answers based on the spreadsheet content. Always reference specific sheets, columns, and data points when relevant."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=1500,
                temperature=0.1,
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            return f"❌ Error answering question: {e}"
    
    def interactive_qa(self):
        """Start interactive Q&A session."""
        print("\n🤖 Excel Data Q&A System")
        print(f"📊 File processed with {len(self.excel_data)} sheets")
        print("💡 Ask questions about the Excel data. Type 'quit' to exit.")
        print("=" * 60)
        
        while True:
            try:
                question = input("\n❓ Your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if not question:
                    continue
                
                print("\n💭 Answer:")
                answer = self.ask_question(question)
                print(answer)
                print("\n" + "-" * 50)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


def main():
    """Main function."""
    if not LIBS_AVAILABLE:
        print("❌ Required libraries not available")
        print("Install with: pip install pandas openpyxl openai")
        return
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        return
    
    # Check if Excel file exists
    excel_file = "../xcell_graphs.xlsx"
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    # Initialize system
    qa_system = ExcelQA(api_key)
    
    # Process Excel file
    if not qa_system.process_excel_file(excel_file):
        return
    
    # Show file overview
    print("\n📊 EXCEL FILE OVERVIEW:")
    print("=" * 40)
    for sheet_name, df in qa_system.excel_data.items():
        print(f"📋 {sheet_name}: {len(df)} rows × {len(df.columns)} columns")
    
    # Demo questions
    demo_questions = [
        "What is this Excel file about?",
        "What are the different sheets in this file?",
        "What student performance data is available?",
        "Who are the students and what subjects are tracked?",
        "What are the average scores for each student?",
        "Which student performs best in Math?",
        "What speedometer mockups are included?"
    ]
    
    print("\n🎯 Demo Questions:")
    print("=" * 30)
    
    for question in demo_questions:
        print(f"\n❓ {question}")
        answer = qa_system.ask_question(question)
        print(f"💭 {answer}")
        print("-" * 50)
    
    # Start interactive session
    qa_system.interactive_qa()


if __name__ == "__main__":
    main()
