#!/usr/bin/env python3
"""
Simple PDF Upload Test
Tests the core PDF upload and agent processing functionality
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_agent_system():
    """Test the agent system directly"""
    try:
        print("🤖 Testing Agent System...")
        
        # Import the orchestrator
        from app.agents.orchestrator import AgentOrchestrator
        
        # Initialize orchestrator
        orchestrator = AgentOrchestrator()
        print("✅ Agent orchestrator initialized")
        
        # Check system status
        status = orchestrator.get_workflow_status()
        print("📊 System Status:")
        print(f"   - Workflow compiled: {status['workflow_compiled']}")
        print(f"   - Checkpointer enabled: {status['checkpointer_enabled']}")
        print(f"   - Number of agents: {len(status['agents'])}")
        
        # Test basic conversation
        print("\n💬 Testing basic conversation...")
        result = await orchestrator.process_message(
            message="Hello! I'm interested in franchise opportunities.",
            session_id="test_session_001"
        )
        
        print("✅ Conversation test result:")
        print(f"   - Success: {result['success']}")
        print(f"   - Response: {result.get('response', 'No response')[:100]}...")
        print(f"   - Intent: {result.get('intent', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent system test failed: {str(e)}")
        return False


async def test_pdf_processing():
    """Test PDF processing with the Coochie Information pack"""
    try:
        print("\n📄 Testing PDF Processing...")
        
        pdf_path = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
        
        # Check if file exists
        if not os.path.exists(pdf_path):
            print(f"❌ PDF file not found: {pdf_path}")
            return False
        
        print(f"✅ PDF file found: {pdf_path}")
        file_size = os.path.getsize(pdf_path)
        print(f"   - File size: {file_size:,} bytes")
        
        # Import the orchestrator
        from app.agents.orchestrator import AgentOrchestrator
        
        # Initialize orchestrator
        orchestrator = AgentOrchestrator()
        
        # Test document processing
        print("🔄 Processing PDF through agent system...")
        
        context = {
            "document_path": pdf_path,
            "original_filename": "Coochie_Information pack.pdf",
            "file_size": file_size,
            "document_type": "brochure",
            "source": "test_upload"
        }
        
        result = await orchestrator.process_message(
            message="Please process the uploaded brochure document: Coochie_Information pack.pdf",
            session_id="pdf_test_session",
            context=context
        )
        
        print("✅ PDF processing result:")
        print(f"   - Success: {result['success']}")
        print(f"   - Response: {result.get('response', 'No response')[:150]}...")
        print(f"   - Intent: {result.get('intent', 'Unknown')}")
        print(f"   - Document ID: {result.get('document_id', 'None')}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ PDF processing test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_qna_functionality():
    """Test Q&A functionality after document processing"""
    try:
        print("\n❓ Testing Q&A Functionality...")
        
        # Import the orchestrator
        from app.agents.orchestrator import AgentOrchestrator
        
        # Initialize orchestrator
        orchestrator = AgentOrchestrator()
        
        # Test questions about the franchise
        test_questions = [
            "What are the investment requirements for this franchise?",
            "Tell me about the franchise fees.",
            "What support is provided to franchisees?",
            "What are the territory requirements?",
            "How much working capital is needed?"
        ]
        
        session_id = "qna_test_session"
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔍 Question {i}: {question}")
            
            result = await orchestrator.process_message(
                message=question,
                session_id=session_id
            )
            
            if result['success']:
                response = result.get('response', 'No response')
                intent = result.get('intent', 'Unknown')
                
                print(f"✅ Answer: {response[:200]}...")
                print(f"📍 Intent: {intent}")
            else:
                print(f"❌ Failed to get answer: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Q&A functionality test failed: {str(e)}")
        return False


async def test_file_validation():
    """Test file type validation"""
    try:
        print("\n🚫 Testing File Validation...")
        
        # Test PDF validation in document tools
        from app.agents.tools.document_tools import ExtractTextTool
        
        tool = ExtractTextTool()
        
        # Test with PDF file (should work)
        pdf_result = await tool._arun(file_path="/path/to/document.pdf")
        if "Only PDF files are supported" not in pdf_result:
            print("✅ PDF files are accepted")
        else:
            print("❌ PDF files are being rejected")
            return False
        
        # Test with non-PDF file (should be rejected)
        docx_result = await tool._arun(file_path="/path/to/document.docx")
        if "Only PDF files are supported" in docx_result:
            print("✅ Non-PDF files are correctly rejected")
        else:
            print("❌ Non-PDF files are not being rejected")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ File validation test failed: {str(e)}")
        return False


async def main():
    """Main test function"""
    print("🧪 Simple PDF Upload and Ingestion Test")
    print("=" * 50)
    
    tests = [
        ("Agent System", test_agent_system),
        ("PDF Processing", test_pdf_processing),
        ("Q&A Functionality", test_qna_functionality),
        ("File Validation", test_file_validation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {str(e)}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)
