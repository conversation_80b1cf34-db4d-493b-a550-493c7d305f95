#!/usr/bin/env python3
"""
Script to check leads in database and conversation messages
"""

import asyncio
import sys
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text

from app.core.database.connection import get_db
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage

async def check_leads():
    """Check leads in the database"""
    
    print("👥 Checking Leads in Database")
    print("=" * 40)
    
    try:
        async for db in get_db():
            # Get total lead count
            total_query = select(func.count(Lead.id)).where(Lead.is_deleted == False)
            total_result = await db.execute(total_query)
            total_leads = total_result.scalar()
            
            print(f"📊 Total active leads: {total_leads}")
            
            if total_leads == 0:
                print("❌ No leads found in database!")
                print("💡 You need to create a lead first for the webhook to work")
                return False
            
            # Get some sample leads
            sample_query = select(Lead).where(Lead.is_deleted == False).limit(5)
            sample_result = await db.execute(sample_query)
            sample_leads = sample_result.scalars().all()
            
            print(f"\n📋 Sample leads:")
            for lead in sample_leads:
                print(f"  - ID: {lead.id}")
                print(f"    Name: {lead.first_name} {lead.last_name}")
                print(f"    Phone: {lead.phone}")
                print(f"    Mobile: {getattr(lead, 'mobile', 'N/A')}")
                print(f"    Email: {lead.email}")
                print()
            
            # Check for test phone numbers
            test_phones = ["+1234567890", "1234567890", "+61234567890", "61234567890"]
            
            print(f"🔍 Checking for test phone numbers: {test_phones}")
            
            for test_phone in test_phones:
                phone_query = select(Lead).where(
                    Lead.is_deleted == False,
                    (Lead.phone == test_phone) | (getattr(Lead, 'mobile', None) == test_phone)
                )
                phone_result = await db.execute(phone_query)
                matching_lead = phone_result.scalar_one_or_none()
                
                if matching_lead:
                    print(f"✅ Found lead for {test_phone}: {matching_lead.first_name} {matching_lead.last_name} (ID: {matching_lead.id})")
                else:
                    print(f"❌ No lead found for {test_phone}")
            
            break  # Exit the async generator loop
            
        return total_leads > 0
        
    except Exception as e:
        print(f"❌ Error checking leads: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

async def check_conversation_messages():
    """Check conversation messages in the database"""
    
    print("\n💬 Checking Conversation Messages in Database")
    print("=" * 50)
    
    try:
        async for db in get_db():
            # Check if table exists
            try:
                table_check = await db.execute(text("SELECT COUNT(*) FROM conversation_message"))
                table_exists = True
            except Exception as e:
                print(f"❌ conversation_message table doesn't exist: {e}")
                print("💡 Run database migration: alembic upgrade head")
                return False
            
            # Get total message count
            total_query = select(func.count(ConversationMessage.id)).where(ConversationMessage.is_deleted == False)
            total_result = await db.execute(total_query)
            total_messages = total_result.scalar()
            
            print(f"📊 Total conversation messages: {total_messages}")
            
            if total_messages == 0:
                print("❌ No conversation messages found!")
                print("💡 Messages should be created when webhooks are processed")
            else:
                # Get recent messages
                recent_query = (
                    select(ConversationMessage)
                    .where(ConversationMessage.is_deleted == False)
                    .order_by(ConversationMessage.created_at.desc())
                    .limit(5)
                )
                recent_result = await db.execute(recent_query)
                recent_messages = recent_result.scalars().all()
                
                print(f"\n📋 Recent conversation messages:")
                for msg in recent_messages:
                    print(f"  - ID: {msg.id}")
                    print(f"    Lead ID: {msg.lead_id}")
                    print(f"    Franchisor ID: {msg.franchisor_id}")
                    print(f"    Sender: {msg.sender}")
                    print(f"    Message: {msg.message[:50]}...")
                    print(f"    Created: {msg.created_at}")
                    print()
                
                # Get message stats by sender
                stats_query = (
                    select(ConversationMessage.sender, func.count(ConversationMessage.id))
                    .where(ConversationMessage.is_deleted == False)
                    .group_by(ConversationMessage.sender)
                )
                stats_result = await db.execute(stats_query)
                stats = dict(stats_result.fetchall())
                
                print(f"📊 Messages by sender:")
                for sender, count in stats.items():
                    print(f"  - {sender}: {count} messages")
            
            break  # Exit the async generator loop
            
        return total_messages
        
    except Exception as e:
        print(f"❌ Error checking conversation messages: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return 0

async def create_test_lead():
    """Create a test lead for webhook testing"""
    
    print("\n👤 Creating Test Lead")
    print("=" * 30)
    
    try:
        async for db in get_db():
            # Check if test lead already exists
            existing_query = select(Lead).where(
                Lead.phone == "+1234567890",
                Lead.is_deleted == False
            )
            existing_result = await db.execute(existing_query)
            existing_lead = existing_result.scalar_one_or_none()
            
            if existing_lead:
                print(f"✅ Test lead already exists: {existing_lead.first_name} {existing_lead.last_name} (ID: {existing_lead.id})")
                return existing_lead.id
            
            # Create new test lead
            test_lead = Lead(
                first_name="Test",
                last_name="Webhook",
                phone="+1234567890",
                email="<EMAIL>",
                is_active=True,
                is_deleted=False
            )
            
            db.add(test_lead)
            await db.commit()
            await db.refresh(test_lead)
            
            print(f"✅ Created test lead: {test_lead.first_name} {test_lead.last_name} (ID: {test_lead.id})")
            print(f"📱 Phone: {test_lead.phone}")
            print(f"📧 Email: {test_lead.email}")
            
            break  # Exit the async generator loop
            
        return test_lead.id
        
    except Exception as e:
        print(f"❌ Error creating test lead: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return None

async def main():
    """Main function to run all checks"""
    
    print("🔍 Database Check for Webhook Conversation Integration")
    print("=" * 60)
    
    # Check leads
    has_leads = await check_leads()
    
    # Check conversation messages
    message_count = await check_conversation_messages()
    
    # Create test lead if none exist
    if not has_leads:
        print(f"\n💡 Creating test lead for webhook testing...")
        test_lead_id = await create_test_lead()
        if test_lead_id:
            print(f"✅ Test lead created successfully!")
        else:
            print(f"❌ Failed to create test lead")
    
    # Summary
    print(f"\n📊 Summary")
    print("=" * 20)
    print(f"Leads in database: {'✅ Yes' if has_leads else '❌ No'}")
    print(f"Conversation messages: {message_count}")
    print(f"Test lead available: {'✅ Yes' if has_leads else '❌ No'}")
    
    if has_leads and message_count == 0:
        print(f"\n💡 Next steps:")
        print(f"1. Start your FastAPI server")
        print(f"2. Send a webhook to: POST /api/webhooks/webhooks/kudosity")
        print(f"3. Use phone number: +1234567890")
        print(f"4. Check server logs for debug messages")
        print(f"5. Run this script again to see if messages were created")
    
    print(f"\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
