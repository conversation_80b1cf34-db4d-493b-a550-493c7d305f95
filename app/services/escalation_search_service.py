"""
Service for searching similar questions in escalation_question_bank using vector embeddings
"""
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, text
from difflib import SequenceMatcher
import re
from openai import OpenAI
import os

from app.models.lead_reference import EscalationQuestionBank

logger = logging.getLogger(__name__)


class EscalationSearchService:
    """Service for searching similar resolved questions in escalation_question_bank"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        # Initialize OpenAI client for embeddings
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI"""
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input=text.strip()
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to generate embedding: {str(e)}")
            return []
    
    async def search_similar_resolved_questions(
        self,
        question: str,
        franchisor_id: str,
        similarity_threshold: float = 0.6,
        limit: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Search for similar resolved questions in escalation_question_bank using vector similarity

        Args:
            question: The question to search for
            franchisor_id: Franchisor ID to filter by
            similarity_threshold: Minimum similarity score (0.0 to 1.0)
            limit: Maximum number of results to return

        Returns:
            List of similar resolved questions with answers
        """
        try:
            logger.info(f"Searching for similar resolved questions using vector similarity: {question[:50]}...")
            logger.info(f"Search parameters: similarity_threshold={similarity_threshold}, limit={limit}")

            # Generate embedding for the input question
            question_embedding = self._generate_embedding(question)
            if not question_embedding:
                logger.error("Failed to generate embedding for question")
                return []

            logger.info(f"Generated embedding for question, length: {len(question_embedding)}")

            # Use vector similarity search with PostgreSQL
            # Convert similarity threshold to distance threshold (1 - cosine_similarity)
            distance_threshold = 1.0 - similarity_threshold

            # Convert embedding to string format for PostgreSQL
            embedding_str = str(question_embedding).replace("'", '"')

            # Search for resolved answers within the SAME franchisor only
            # Escalated questions and answers are bound to specific franchisor
            query = text(f"""
                SELECT
                    id,
                    name,
                    answer,
                    support_status,
                    created_at,
                    updated_at,
                    1 - (embedding <=> '{embedding_str}'::vector) as similarity_score
                FROM escalation_question_bank
                WHERE franchisor_id = '{franchisor_id}'
                    AND support_status = 'resolved'
                    AND answer IS NOT NULL
                    AND is_active = true
                    AND is_deleted = false
                    AND embedding IS NOT NULL
                    AND (embedding <=> '{embedding_str}'::vector) <= {distance_threshold}
                ORDER BY embedding <=> '{embedding_str}'::vector
                LIMIT {limit}
            """)

            logger.info(f"Executing vector search query with distance_threshold: {distance_threshold}")
            result = await self.db.execute(query)

            similar_questions = []
            for row in result.fetchall():
                similar_questions.append({
                    'id': str(row.id),
                    'question': row.name,
                    'answer': row.answer,
                    'similarity_score': float(row.similarity_score),
                    'support_status': row.support_status,
                    'created_at': row.created_at,
                    'updated_at': row.updated_at
                })

            logger.info(f"Vector search returned {len(similar_questions)} results")

            if similar_questions:
                logger.info(f"Found {len(similar_questions)} similar resolved questions using vector search")
                for result in similar_questions:
                    logger.info(f"Similar question: '{result['question'][:50]}...' (similarity: {result['similarity_score']:.3f})")
            else:
                logger.info("No similar resolved questions found above threshold using vector search")
                # Fallback to text-based search if no vector results
                logger.info("Falling back to text-based similarity search...")
                return await self._fallback_text_search(question, franchisor_id, similarity_threshold, limit)

            return similar_questions

        except Exception as e:
            logger.error(f"Error in vector search, falling back to text search: {str(e)}")
            return await self._fallback_text_search(question, franchisor_id, similarity_threshold, limit)

    async def _fallback_text_search(
        self,
        question: str,
        franchisor_id: str,
        similarity_threshold: float,
        limit: int
    ) -> List[Dict[str, Any]]:
        """Fallback to text-based similarity search when vector search fails"""
        try:
            # Get resolved questions for the SAME franchisor only
            # Escalated questions and answers are bound to specific franchisor
            query = select(EscalationQuestionBank).where(
                and_(
                    EscalationQuestionBank.franchisor_id == franchisor_id,
                    EscalationQuestionBank.support_status == 'resolved',
                    EscalationQuestionBank.answer.is_not(None),
                    EscalationQuestionBank.is_active == True,
                    EscalationQuestionBank.is_deleted == False
                )
            )

            result = await self.db.execute(query)
            resolved_questions = result.scalars().all()

            if not resolved_questions:
                return []

            # Calculate similarity scores using text matching
            similar_questions = []
            question_clean = self._clean_text(question)

            for resolved_q in resolved_questions:
                resolved_clean = self._clean_text(resolved_q.name)
                similarity = self._calculate_similarity(question_clean, resolved_clean)

                if similarity >= similarity_threshold:
                    similar_questions.append({
                        'id': str(resolved_q.id),
                        'question': resolved_q.name,
                        'answer': resolved_q.answer,
                        'similarity_score': similarity,
                        'support_status': resolved_q.support_status,
                        'created_at': resolved_q.created_at,
                        'updated_at': resolved_q.updated_at
                    })

            # Sort by similarity score (highest first)
            similar_questions.sort(key=lambda x: x['similarity_score'], reverse=True)
            return similar_questions[:limit]

        except Exception as e:
            logger.error(f"Error in fallback text search: {str(e)}")
            return []

    async def store_question_embedding(self, question_id: str, question_text: str) -> bool:
        """Generate and store embedding for a question in escalation_question_bank"""
        try:
            # Generate embedding
            embedding = self._generate_embedding(question_text)
            if not embedding:
                return False

            # Update the question with embedding
            embedding_str = str(embedding).replace("'", '"')
            update_query = text(f"""
                UPDATE escalation_question_bank
                SET embedding = '{embedding_str}'::vector
                WHERE id = '{question_id}'
            """)

            await self.db.execute(update_query)
            await self.db.commit()

            logger.info(f"Stored embedding for question: {question_text[:50]}...")
            return True

        except Exception as e:
            logger.error(f"Failed to store embedding for question: {str(e)}")
            return False

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text for comparison"""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Remove punctuation for better matching
        text = re.sub(r'[^\w\s]', '', text)
        
        return text
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts using SequenceMatcher"""
        if not text1 or not text2:
            return 0.0
        
        # Use SequenceMatcher for basic similarity
        similarity = SequenceMatcher(None, text1, text2).ratio()
        
        # Boost similarity for common question patterns
        similarity = self._boost_question_similarity(text1, text2, similarity)
        
        return similarity
    
    def _boost_question_similarity(self, text1: str, text2: str, base_similarity: float) -> float:
        """Boost similarity for questions with similar patterns and penalize very different topics"""

        # Common question words
        question_words = ['what', 'how', 'when', 'where', 'why', 'who', 'which']

        # Check if both are questions
        text1_is_question = any(word in text1 for word in question_words) or text1.endswith('?')
        text2_is_question = any(word in text2 for word in question_words) or text2.endswith('?')

        # Check for topic similarity - penalize very different topics
        sports_terms = ['football', 'game', 'match', 'sport', 'score', 'winner', 'won', 'team']
        business_terms = ['ceo', 'company', 'business', 'executive', 'management', 'director', 'founder']
        franchise_terms = ['franchise', 'fee', 'cost', 'investment', 'royalty', 'training', 'support']

        text1_sports = any(term in text1 for term in sports_terms)
        text2_sports = any(term in text2 for term in sports_terms)
        text1_business = any(term in text1 for term in business_terms)
        text2_business = any(term in text2 for term in business_terms)
        text1_franchise = any(term in text1 for term in franchise_terms)
        text2_franchise = any(term in text2 for term in franchise_terms)

        # Penalize if topics are very different
        if (text1_sports and text2_business) or (text1_business and text2_sports):
            # Sports vs business questions are very different
            base_similarity *= 0.3  # Heavy penalty
        elif (text1_sports and text2_franchise) or (text1_franchise and text2_sports):
            # Sports vs franchise questions are different
            base_similarity *= 0.4  # Heavy penalty
        elif text1_is_question and text2_is_question:
            # Both are questions, slight boost
            base_similarity *= 1.05

        # Boost for similar topics
        if text1_franchise and text2_franchise:
            # Both contain franchise terms
            text1_terms = set(word for word in text1.split() if word in franchise_terms)
            text2_terms = set(word for word in text2.split() if word in franchise_terms)
            common_terms = text1_terms.intersection(text2_terms)
            if common_terms:
                boost = len(common_terms) / max(len(text1_terms), len(text2_terms))
                base_similarity *= (1 + boost * 0.2)  # Up to 20% boost

        # Cap at 1.0
        return min(base_similarity, 1.0)
    
    def format_resolved_answer(self, resolved_question: Dict[str, Any]) -> str:
        """Format a resolved question's answer for display with intelligent answer selection"""
        try:
            question = resolved_question['question']
            answer = resolved_question['answer']
            similarity = resolved_question['similarity_score']

            # Handle different answer formats and select the best answer
            if isinstance(answer, dict):
                if 'text' in answer:
                    answer_text = answer['text']
                elif 'content' in answer:
                    answer_text = answer['content']
                else:
                    answer_text = str(answer)
            elif isinstance(answer, list):
                # Intelligently select the best answer from multiple options
                answer_text = self._select_best_answer(question, answer)
                logger.info(f"Selected answer from {len(answer)} options for question: '{question[:50]}...'")
            else:
                answer_text = str(answer)

            # Generate natural response based on question type
            natural_response = self._generate_natural_response(question, answer_text)

            return natural_response

        except Exception as e:
            logger.error(f"Error formatting resolved answer: {str(e)}")
            return "I found a similar question that was previously answered, but I'm having trouble formatting the response. Please ask your question again or contact support."

    def _generate_natural_response(self, original_question: str, answer_data: str) -> str:
        """Generate a natural language response based on the question and answer"""
        question_lower = original_question.lower()

        # Sports-related questions
        if any(word in question_lower for word in ['football', 'game', 'match', 'sport', 'score', 'winner', 'won']):
            if 'australia' in answer_data.lower():
                return f"Based on previous information, Australia won the football game last night.\n\nHowever, I'm primarily here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"
            else:
                return f"Based on previous information: {answer_data}\n\nHowever, I'm primarily here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

        # Weather-related questions
        elif any(word in question_lower for word in ['weather', 'temperature', 'rain', 'sunny', 'cloudy']):
            return f"Based on previous information: {answer_data}\n\nHowever, I don't have access to current weather information. I'm here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

        # News/politics questions
        elif any(word in question_lower for word in ['news', 'politics', 'election', 'government']):
            return f"Based on previous information: {answer_data}\n\nHowever, I don't have access to current news or political information. I'm here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

        # Franchise-related questions that were escalated
        elif any(word in question_lower for word in ['franchise', 'training', 'support', 'fee', 'cost', 'investment']):
            return f"Great question! Based on previous similar inquiries: {answer_data}\n\nDo you have any other questions about our franchise opportunity? (Type 'done' when finished)"

        # Generic response for other topics
        else:
            return f"Based on previous similar questions: {answer_data}\n\nI'm here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

    def _select_best_answer(self, question: str, answers: list) -> str:
        """Intelligently select the most appropriate answer from multiple options"""
        if not answers:
            return "No answer available"

        if len(answers) == 1:
            return str(answers[0])

        question_lower = question.lower()

        # Score each answer based on various criteria
        scored_answers = []
        for answer in answers:
            answer_str = str(answer)
            score = self._calculate_answer_score(question_lower, answer_str)
            scored_answers.append((answer_str, score))

        # Sort by score (highest first) and return the best answer
        scored_answers.sort(key=lambda x: x[1], reverse=True)
        best_answer = scored_answers[0][0]

        logger.info(f"Selected best answer from {len(answers)} options: '{best_answer[:50]}...' (score: {scored_answers[0][1]:.2f})")
        return best_answer

    def _calculate_answer_score(self, question_lower: str, answer: str) -> float:
        """Calculate relevance score for an answer based on the question"""
        answer_lower = answer.lower()
        score = 0.0

        # Base score for answer length (prefer substantial but not too long answers)
        length = len(answer)
        if 10 <= length <= 200:
            score += 2.0
        elif 5 <= length <= 300:
            score += 1.0
        elif length < 5:
            score -= 1.0  # Too short
        elif length > 500:
            score -= 0.5  # Too long

        # Question type specific scoring
        if any(word in question_lower for word in ['how much', 'cost', 'price', 'fee', 'money']):
            # Cost questions - prefer answers with numbers or currency
            has_numbers = any(char.isdigit() for char in answer_lower)
            has_currency = any(symbol in answer_lower for symbol in ['$', 'dollar', 'aud', '%', 'percent'])
            if has_numbers or has_currency:
                score += 3.0
            if any(word in answer_lower for word in ['free', 'included', 'no cost']):
                score += 2.0

        elif any(word in question_lower for word in ['when', 'how long', 'timeline', 'time']):
            # Time questions - prefer answers with temporal information
            if any(word in answer_lower for word in ['day', 'week', 'month', 'year', 'hour', 'immediately', 'soon']):
                score += 3.0

        elif any(word in question_lower for word in ['where', 'location', 'address']):
            # Location questions - prefer answers with place information
            if any(word in answer_lower for word in ['address', 'location', 'city', 'state', 'street', 'area']):
                score += 3.0

        elif any(word in question_lower for word in ['what', 'which', 'describe', 'explain']):
            # Descriptive questions - prefer detailed answers
            if length > 50:
                score += 2.0
            if any(word in answer_lower for word in ['include', 'provide', 'offer', 'feature']):
                score += 1.0

        elif any(word in question_lower for word in ['yes', 'no', 'can', 'do you', 'is', 'are']):
            # Yes/No questions - prefer clear yes/no answers
            if any(word in answer_lower for word in ['yes', 'no', 'absolutely', 'definitely', 'certainly']):
                score += 3.0

        # Prefer more informative answers
        if any(word in answer_lower for word in ['because', 'since', 'due to', 'reason']):
            score += 1.0  # Explanatory answers

        # Prefer professional/helpful tone
        if any(phrase in answer_lower for phrase in ['happy to help', 'please', 'thank you', 'welcome']):
            score += 0.5

        # Penalize vague answers
        if any(phrase in answer_lower for phrase in ['maybe', 'possibly', 'not sure', 'depends']):
            score -= 1.0

        return score
