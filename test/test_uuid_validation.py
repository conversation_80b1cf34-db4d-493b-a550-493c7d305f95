#!/usr/bin/env python3
"""
Test script to verify UUID validation in document schemas
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.schemas.document import DocumentUploadRequest, DocumentCreateRequest, DocumentUpdateRequest
import uuid


def test_document_upload_request():
    """Test DocumentUploadRequest UUID validation"""
    print("🧪 Testing DocumentUploadRequest UUID validation...")
    
    # Test with valid UUID
    valid_uuid = str(uuid.uuid4())
    request = DocumentUploadRequest(
        name="Test Document",
        description="Test description",
        franchisor_id=valid_uuid,
        is_active=True
    )
    print(f"✅ Valid UUID: {request.franchisor_id}")
    
    # Test with 'string' - should convert to None
    request = DocumentUploadRequest(
        name="Test Document",
        description="Test description", 
        franchisor_id="string",
        is_active=True
    )
    print(f"✅ 'string' converted to: {request.franchisor_id}")
    
    # Test with empty string - should convert to None
    request = DocumentUploadRequest(
        name="Test Document",
        description="Test description",
        franchisor_id="",
        is_active=True
    )
    print(f"✅ Empty string converted to: {request.franchisor_id}")
    
    # Test with None - should remain None
    request = DocumentUploadRequest(
        name="Test Document",
        description="Test description",
        franchisor_id=None,
        is_active=True
    )
    print(f"✅ None remains: {request.franchisor_id}")
    
    # Test with invalid UUID - should convert to None
    request = DocumentUploadRequest(
        name="Test Document",
        description="Test description",
        franchisor_id="invalid-uuid-123",
        is_active=True
    )
    print(f"✅ Invalid UUID converted to: {request.franchisor_id}")


def test_document_create_request():
    """Test DocumentCreateRequest UUID validation"""
    print("\n🧪 Testing DocumentCreateRequest UUID validation...")
    
    # Test with 'string' for both user_id and franchisor_id
    request = DocumentCreateRequest(
        name="Test Document",
        description="Test description",
        file_path="/test/path.pdf",
        user_id="string",
        franchisor_id="string",
        is_active=True
    )
    print(f"✅ user_id 'string' converted to: {request.user_id}")
    print(f"✅ franchisor_id 'string' converted to: {request.franchisor_id}")


def test_document_update_request():
    """Test DocumentUpdateRequest UUID validation"""
    print("\n🧪 Testing DocumentUpdateRequest UUID validation...")
    
    # Test with 'string' for both user_id and franchisor_id
    request = DocumentUpdateRequest(
        name="Updated Document",
        user_id="string",
        franchisor_id="string"
    )
    print(f"✅ user_id 'string' converted to: {request.user_id}")
    print(f"✅ franchisor_id 'string' converted to: {request.franchisor_id}")


def main():
    """Run all tests"""
    print("🚀 Starting UUID validation tests...\n")
    
    try:
        test_document_upload_request()
        test_document_create_request()
        test_document_update_request()
        
        print("\n✅ All UUID validation tests passed!")
        print("🎯 The fix should prevent the database error when 'string' is passed as franchisor_id")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
