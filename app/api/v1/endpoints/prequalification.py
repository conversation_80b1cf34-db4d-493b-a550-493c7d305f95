"""
Prequalification Question Bank API Endpoints
"""

from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, Query, HTTPException, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload
from app.core.database.connection import get_db
from app.core.security.auth import get_current_user
from app.schemas.prequalification import (
    PreQualificationQuestionResponse,
    PreQualificationQuestionListResponse,
    PreQualificationQuestionSuccessResponse,
    QuestionBankResponse,
    QuestionBankListResponse,
    QuestionBankSuccessResponse,
    EscalationQuestionBankResponse,
    EscalationQuestionBankListResponse,
    EscalationQuestionBankSuccessResponse,
    EscalationAnswerUpdateRequest,
    EscalationStatusUpdateRequest,
    EscalationUpdateSuccessResponse
)
from app.models.pre_qualification_question import PreQualificationQuestion as Question
from app.models.lead_reference import QuestionBank, EscalationQuestionBank
from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.core.logging import logger

router = APIRouter(prefix="/questions_Module", tags=["Questions"])


@router.get(
    "/pre-qualification-questions",
    response_model=PreQualificationQuestionSuccessResponse,
    summary="Get Pre-Qualification Questions",
    description="Retrieve all active pre-qualification questions",
    responses={
        200: {
            "description": "Pre-qualification questions retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Pre-Qualification Questions Retrieved",
                            "description": "Pre-qualification questions retrieved successfully"
                        },
                        "data": {
                            "items": [
                                {
                                    "question_id": "Q005",
                                    "category": "location",
                                    "question_text": "Which location are you interested in operating your franchise?",
                                    "question_internal_text": "Which location are you interested in operating your franchise?",
                                    "question_type": "multiple_choice",
                                    "expected_answer": ["Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide"],
                                    "expected_answer_type": "choice",
                                    "answer_options": "[\"Sydney\", \"Melbourne\", \"Brisbane\", \"Perth\", \"Adelaide\", \"Other\"]",
                                    "score_weight": 25,
                                    "qualification_weight": 0.25,
                                    "passing_criteria": "{\"qualifying_answers\": [\"Sydney\", \"Melbourne\", \"Brisbane\", \"Perth\", \"Adelaide\"]}",
                                    "validation_rules": "{\"required\": true, \"min_selections\": 1, \"max_selections\": 3}",
                                    "requires_follow_up": True,
                                    "follow_up_logic": "If 'Other' is selected, ask for specific location details",
                                    "is_required": True,
                                    "is_active": True,
                                }
                            ],
                            "total_count": 1
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def get_pre_qualification_questions(
    lead_id: Optional[str] = Query(None, description="Filter by lead ID"),
    franchisor_id: Optional[str] = Query(None, description="Filter by franchisor ID"),
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search term for question name"),
    sort_by: Optional[str] = Query("order_sequence", description="Sort by field (name, category, order_sequence, created_at)"),
    sort_order: Optional[str] = Query("asc", description="Sort order (asc, desc)"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all active pre-qualification questions.

    Can be filtered by lead_id, franchisor_id, or category.
    """
    try:
        # Build query
        query = select(Question).where(
            Question.is_active == True,
            Question.is_deleted == False
        )

        # Apply filters
        if lead_id:
            query = query.where(Question.lead_id == lead_id)
        if franchisor_id:
            query = query.where(Question.franchisor_id == franchisor_id)
        if category:
            query = query.where(Question.category == category)
        if search:
            query = query.where(Question.name.ilike(f"%{search}%"))

        # Apply sorting
        sort_column = Question.order_sequence  # default
        if sort_by == "name":
            sort_column = Question.name
        elif sort_by == "category":
            sort_column = Question.category
        elif sort_by == "order_sequence":
            sort_column = Question.order_sequence
        elif sort_by == "created_at":
            sort_column = Question.created_at

        if sort_order == "asc":
            query = query.order_by(sort_column.asc())
        else:
            query = query.order_by(sort_column.desc())

        result = await db.execute(query)
        questions = result.scalars().all()

        # Convert to response format
        items = [
            PreQualificationQuestionResponse(
                id=str(item.id),
                name=item.name,
                lead_id=str(item.lead_id) if item.lead_id else None,
                franchisor_id=str(item.franchisor_id) if item.franchisor_id else None,
                expected_answers=item.expected_answers,
                question_id=item.question_id,
                category=item.category,
                score_weight=item.score_weight,
                context_info=item.context_info,
                qualification_weight=item.qualification_weight,
                expected_answer_type=item.expected_answer_type,
                answer_options=item.answer_options,
                passing_criteria=item.passing_criteria,
                validation_rules=item.validation_rules,
                requires_follow_up=item.requires_follow_up,
                follow_up_logic=item.follow_up_logic,
                is_deleted=item.is_deleted,
                is_active=item.is_active,
                is_required=item.is_required,
                order_sequence=item.order_sequence,
                created_at=item.created_at,
                updated_at=item.updated_at,
                deleted_at=item.deleted_at
            )
            for item in questions
        ]

        return PreQualificationQuestionSuccessResponse(
            success=True,
            status="success",
            message={
                "title": "Pre-Qualification Questions Retrieved",
                "description": "Pre-qualification questions retrieved successfully"
            },
            data=PreQualificationQuestionListResponse(
                items=items,
                total_count=len(items)
            )
        )

    except Exception as e:
        logger.error(f"Error retrieving pre-qualification questions: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve pre-qualification questions")


@router.get(
    "/question-bank",
    response_model=QuestionBankSuccessResponse,
    summary="Get Question Bank (Legacy)",
    description="Retrieve all active question bank items (legacy endpoint)",
    responses={
        200: {
            "description": "Question bank retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Question Bank Retrieved",
                            "description": "Question bank retrieved successfully"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-************",
                                    "name": "What is your investment budget?",
                                    "lead_id": "550e8400-e29b-41d4-a716-************",
                                    "franchisor_id": None,
                                    "is_deleted": False,
                                    "is_active": True,
                                    "created_at": "2024-01-01T00:00:00Z",
                                    "updated_at": "2024-01-01T00:00:00Z"
                                }
                            ],
                            "total_count": 1,
                            "page": 1,
                            "size": 10,
                            "total_pages": 1
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def get_question_bank(
    page: int = Query(1, ge=1, description="Page number (starts from 1)"),
    size: int = Query(10, ge=1, le=100, description="Number of items per page (1-100)"),
    lead_id: Optional[str] = Query(None, description="Filter by lead ID"),
    franchisor_id: Optional[str] = Query(None, description="Filter by franchisor ID"),
    search: Optional[str] = Query(None, description="Search term for question name"),
    sort_by: Optional[str] = Query("created_at", description="Sort by field (name, created_at)"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc, desc)"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all active question bank items.
    
    Can be filtered by lead_id or franchisor_id to get specific questions.
    """
    try:
        # Build query
        query = select(QuestionBank).where(
            QuestionBank.is_active == True,
            QuestionBank.is_deleted == False
        )

        # Apply filters
        if lead_id:
            query = query.where(QuestionBank.lead_id == lead_id)
        if franchisor_id:
            query = query.where(QuestionBank.franchisor_id == franchisor_id)
        if search:
            query = query.where(QuestionBank.name.ilike(f"%{search}%"))

        # Apply sorting
        sort_column = QuestionBank.created_at  # default
        if sort_by == "name":
            sort_column = QuestionBank.name
        elif sort_by == "created_at":
            sort_column = QuestionBank.created_at

        if sort_order == "asc":
            query = query.order_by(sort_column.asc())
        else:
            query = query.order_by(sort_column.desc())

        # Count total items for pagination
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        # Apply pagination
        offset = (page - 1) * size
        paginated_query = query.offset(offset).limit(size)

        result = await db.execute(paginated_query)
        question_bank_items = result.scalars().all()

        # Get unique lead and franchisor IDs
        lead_ids = {item.lead_id for item in question_bank_items if item.lead_id}
        franchisor_ids = {item.franchisor_id for item in question_bank_items if item.franchisor_id}

        # Fetch lead information
        lead_cache = {}
        if lead_ids:
            lead_query = select(Lead).where(Lead.id.in_(lead_ids))
            lead_result = await db.execute(lead_query)
            leads = lead_result.scalars().all()
            for lead in leads:
                lead_cache[lead.id] = {
                    'first_name': lead.first_name,
                    'last_name': lead.last_name
                }

        # Fetch franchisor information
        franchisor_cache = {}
        if franchisor_ids:
            franchisor_query = select(Franchisor).where(Franchisor.id.in_(franchisor_ids))
            franchisor_result = await db.execute(franchisor_query)
            franchisors = franchisor_result.scalars().all()
            for franchisor in franchisors:
                franchisor_cache[franchisor.id] = franchisor.name

        # Convert to response format with lead and franchisor information
        items = []
        for item in question_bank_items:
            lead_info = lead_cache.get(item.lead_id, {})
            franchisor_name = franchisor_cache.get(item.franchisor_id)

            items.append(QuestionBankResponse(
                id=str(item.id),
                name=item.name,
                lead_id=str(item.lead_id) if item.lead_id else None,
                lead_first_name=lead_info.get('first_name'),
                lead_last_name=lead_info.get('last_name'),
                franchisor_id=str(item.franchisor_id) if item.franchisor_id else None,
                franchisor_name=franchisor_name,
                is_deleted=item.is_deleted,
                is_active=item.is_active,
                created_at=item.created_at,
                updated_at=item.updated_at
            ))

        # Calculate pagination info
        total_pages = (total_count + size - 1) // size

        return QuestionBankSuccessResponse(
            success=True,
            status="success",
            message={
                "title": "Question Bank Retrieved",
                "description": f"Question bank retrieved successfully (Page {page} of {total_pages})"
            },
            data=QuestionBankListResponse(
                items=items,
                total_count=total_count,
                page=page,
                size=size,
                total_pages=total_pages
            )
        )
        
    except Exception as e:
        logger.error(f"Error retrieving question bank: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve question bank")


@router.get(
    "/escalation-question-bank",
    response_model=EscalationQuestionBankSuccessResponse,
    summary="Get Escalation Question Bank",
    description="Retrieve all active escalation question bank items",
    responses={
        200: {
            "description": "Escalation question bank retrieved successfully"
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def get_escalation_question_bank(
    page: int = Query(1, ge=1, description="Page number (starts from 1)"),
    size: int = Query(10, ge=1, le=100, description="Number of items per page (1-100)"),
    lead_id: Optional[str] = Query(None, description="Filter by lead ID"),
    franchisor_id: Optional[str] = Query(None, description="Filter by franchisor ID"),
    support_status: Optional[str] = Query(None, description="Filter by support status"),
    search: Optional[str] = Query(None, description="Search term for question name"),
    sort_by: Optional[str] = Query("created_at", description="Sort by field (name, support_status, created_at)"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc, desc)"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all active escalation question bank items.
    
    Can be filtered by lead_id, franchisor_id, or support_status.
    """
    try:
        # Build query
        query = select(EscalationQuestionBank).where(
            EscalationQuestionBank.is_active == True,
            EscalationQuestionBank.is_deleted == False
        )
        
        # Apply filters
        if lead_id:
            query = query.where(EscalationQuestionBank.lead_id == lead_id)
        if franchisor_id:
            query = query.where(EscalationQuestionBank.franchisor_id == franchisor_id)
        if support_status:
            query = query.where(EscalationQuestionBank.support_status == support_status)
        if search:
            query = query.where(EscalationQuestionBank.name.ilike(f"%{search}%"))

        # Apply sorting
        sort_column = EscalationQuestionBank.created_at  # default
        if sort_by == "name":
            sort_column = EscalationQuestionBank.name
        elif sort_by == "support_status":
            sort_column = EscalationQuestionBank.support_status
        elif sort_by == "created_at":
            sort_column = EscalationQuestionBank.created_at

        if sort_order == "asc":
            query = query.order_by(sort_column.asc())
        else:
            query = query.order_by(sort_column.desc())

        # Count total items for pagination
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        # Apply pagination
        offset = (page - 1) * size
        paginated_query = query.offset(offset).limit(size)

        result = await db.execute(paginated_query)
        escalation_items = result.scalars().all()

        # Get unique lead and franchisor IDs
        lead_ids = {item.lead_id for item in escalation_items if item.lead_id}
        franchisor_ids = {item.franchisor_id for item in escalation_items if item.franchisor_id}

        # Fetch lead information
        lead_cache = {}
        if lead_ids:
            lead_query = select(Lead).where(Lead.id.in_(lead_ids))
            lead_result = await db.execute(lead_query)
            leads = lead_result.scalars().all()
            for lead in leads:
                lead_cache[lead.id] = {
                    'first_name': lead.first_name,
                    'last_name': lead.last_name
                }

        # Fetch franchisor information
        franchisor_cache = {}
        if franchisor_ids:
            franchisor_query = select(Franchisor).where(Franchisor.id.in_(franchisor_ids))
            franchisor_result = await db.execute(franchisor_query)
            franchisors = franchisor_result.scalars().all()
            for franchisor in franchisors:
                franchisor_cache[franchisor.id] = franchisor.name

        # Convert to response format with lead and franchisor information
        items = []
        for item in escalation_items:
            # Handle JSONB answer field
            answer = item.answer if item.answer else None

            lead_info = lead_cache.get(item.lead_id, {})
            franchisor_name = franchisor_cache.get(item.franchisor_id)

            items.append(EscalationQuestionBankResponse(
                id=str(item.id),
                name=item.name,
                lead_id=str(item.lead_id) if item.lead_id else None,
                lead_first_name=lead_info.get('first_name'),
                lead_last_name=lead_info.get('last_name'),
                franchisor_id=str(item.franchisor_id) if item.franchisor_id else None,
                franchisor_name=franchisor_name,
                answer=answer,
                support_status=item.support_status,
                is_deleted=item.is_deleted,
                is_active=item.is_active,
                created_at=item.created_at,
                updated_at=item.updated_at
            ))

        # Calculate pagination info
        total_pages = (total_count + size - 1) // size

        return EscalationQuestionBankSuccessResponse(
            success=True,
            status="success",
            message={
                "title": "Escalation Question Bank Retrieved",
                "description": f"Escalation question bank retrieved successfully (Page {page} of {total_pages})"
            },
            data=EscalationQuestionBankListResponse(
                items=items,
                total_count=total_count,
                page=page,
                size=size,
                total_pages=total_pages
            )
        )
        
    except Exception as e:
        logger.error(f"Error retrieving escalation question bank: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve escalation question bank")


@router.put(
    "/escalation-question-bank/{escalation_id}/answer",
    response_model=EscalationUpdateSuccessResponse,
    summary="Update Escalation Question Answer",
    description="Update the answer for an escalation question and mark it as resolved",
    responses={
        200: {
            "description": "Escalation question answer updated successfully"
        },
        404: {"description": "Escalation question not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def update_escalation_answer(
    escalation_id: str = Path(..., description="Escalation question ID"),
    answer_data: EscalationAnswerUpdateRequest = ...,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update the answer for an escalation question.

    When an answer is successfully submitted, the support_status is automatically set to 'Resolved'.
    """
    try:
        # Find the escalation question
        query = select(EscalationQuestionBank).where(
            EscalationQuestionBank.id == escalation_id,
            EscalationQuestionBank.is_deleted == False
        )

        result = await db.execute(query)
        escalation_item = result.scalar_one_or_none()

        if not escalation_item:
            raise HTTPException(status_code=404, detail="Escalation question not found")

        # Update the answer and mark as resolved
        escalation_item.answer = answer_data.answer  # Store as JSONB directly
        escalation_item.support_status = "Resolved"

        await db.commit()
        await db.refresh(escalation_item)

        # Get answer from JSONB field
        answer = escalation_item.answer if escalation_item.answer else None

        # Fetch lead information
        lead_first_name = None
        lead_last_name = None
        if escalation_item.lead_id:
            lead_query = select(Lead).where(Lead.id == escalation_item.lead_id)
            lead_result = await db.execute(lead_query)
            lead = lead_result.scalar_one_or_none()
            if lead:
                lead_first_name = lead.first_name
                lead_last_name = lead.last_name

        # Fetch franchisor information
        franchisor_name = None
        if escalation_item.franchisor_id:
            franchisor_query = select(Franchisor).where(Franchisor.id == escalation_item.franchisor_id)
            franchisor_result = await db.execute(franchisor_query)
            franchisor = franchisor_result.scalar_one_or_none()
            if franchisor:
                franchisor_name = franchisor.name

        response_data = EscalationQuestionBankResponse(
            id=str(escalation_item.id),
            name=escalation_item.name,
            lead_id=str(escalation_item.lead_id) if escalation_item.lead_id else None,
            lead_first_name=lead_first_name,
            lead_last_name=lead_last_name,
            franchisor_id=str(escalation_item.franchisor_id) if escalation_item.franchisor_id else None,
            franchisor_name=franchisor_name,
            answer=answer,
            support_status=escalation_item.support_status,
            is_deleted=escalation_item.is_deleted,
            is_active=escalation_item.is_active,
            created_at=escalation_item.created_at,
            updated_at=escalation_item.updated_at
        )

        return EscalationUpdateSuccessResponse(
            success=True,
            status="success",
            message={
                "title": "Answer Updated",
                "description": "Escalation question answer updated and marked as resolved"
            },
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating escalation answer: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update escalation answer")


@router.put(
    "/escalation-question-bank/{escalation_id}/status",
    response_model=EscalationUpdateSuccessResponse,
    summary="Update Escalation Question Status",
    description="Update the support status for an escalation question",
    responses={
        200: {
            "description": "Escalation question status updated successfully"
        },
        404: {"description": "Escalation question not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def update_escalation_status(
    escalation_id: str = Path(..., description="Escalation question ID"),
    status_data: EscalationStatusUpdateRequest = ...,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update the support status for an escalation question.

    Common status values: 'Pending', 'In Progress', 'Resolved', 'Closed'
    """
    try:
        # Find the escalation question
        query = select(EscalationQuestionBank).where(
            EscalationQuestionBank.id == escalation_id,
            EscalationQuestionBank.is_deleted == False
        )

        result = await db.execute(query)
        escalation_item = result.scalar_one_or_none()

        if not escalation_item:
            raise HTTPException(status_code=404, detail="Escalation question not found")

        # Update the status
        escalation_item.support_status = status_data.support_status

        await db.commit()
        await db.refresh(escalation_item)

        # Get answer from JSONB field
        answer = escalation_item.answer if escalation_item.answer else None

        # Fetch lead information
        lead_first_name = None
        lead_last_name = None
        if escalation_item.lead_id:
            lead_query = select(Lead).where(Lead.id == escalation_item.lead_id)
            lead_result = await db.execute(lead_query)
            lead = lead_result.scalar_one_or_none()
            if lead:
                lead_first_name = lead.first_name
                lead_last_name = lead.last_name

        # Fetch franchisor information
        franchisor_name = None
        if escalation_item.franchisor_id:
            franchisor_query = select(Franchisor).where(Franchisor.id == escalation_item.franchisor_id)
            franchisor_result = await db.execute(franchisor_query)
            franchisor = franchisor_result.scalar_one_or_none()
            if franchisor:
                franchisor_name = franchisor.name

        response_data = EscalationQuestionBankResponse(
            id=str(escalation_item.id),
            name=escalation_item.name,
            lead_id=str(escalation_item.lead_id) if escalation_item.lead_id else None,
            lead_first_name=lead_first_name,
            lead_last_name=lead_last_name,
            franchisor_id=str(escalation_item.franchisor_id) if escalation_item.franchisor_id else None,
            franchisor_name=franchisor_name,
            answer=answer,
            support_status=escalation_item.support_status,
            is_deleted=escalation_item.is_deleted,
            is_active=escalation_item.is_active,
            created_at=escalation_item.created_at,
            updated_at=escalation_item.updated_at
        )

        return EscalationUpdateSuccessResponse(
            success=True,
            status="success",
            message={
                "title": "Status Updated",
                "description": f"Escalation question status updated to '{status_data.support_status}'"
            },
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating escalation status: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update escalation status")
