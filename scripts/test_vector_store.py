"""
Tests for vector storage and embedding functionality.

This module tests the vector embedding and storage components
to ensure proper functionality without requiring API keys.
"""

import pytest
from unittest.mock import Mock, patch
import numpy as np

from ingest.core import DocumentChunk, FileType, LanguageCode, VectorEntry
from ingest.vector_store import OpenAIEmbedder, FAISSVectorStorage, VectorStore


class TestFAISSVectorStorage:
    """Test FAISS vector storage functionality."""
    
    @pytest.mark.skipif(True, reason="FAISS may not be available in test environment")
    def test_initialize_storage(self, temp_dir):
        """Test initializing FAISS storage."""
        storage = FAISSVectorStorage(index_path=temp_dir, dimension=128)
        
        assert storage.dimension == 128
        assert storage.index_path == temp_dir
        assert storage.index.ntotal == 0
    
    @pytest.mark.skipif(True, reason="FAISS may not be available in test environment")
    def test_store_vectors(self, temp_dir):
        """Test storing vectors in FAISS index."""
        storage = FAISSVectorStorage(index_path=temp_dir, dimension=3)
        
        # Create test vectors
        vectors = [
            VectorEntry(
                vector=np.array([1.0, 0.0, 0.0]),
                chunk_id="chunk-1",
                source_file="test1.txt",
            ),
            VectorEntry(
                vector=np.array([0.0, 1.0, 0.0]),
                chunk_id="chunk-2", 
                source_file="test2.txt",
            ),
        ]
        
        # Store vectors
        success = storage.store_vectors(vectors)
        assert success
        assert storage.index.ntotal == 2
        assert len(storage.metadata_store) == 2
    
    @pytest.mark.skipif(True, reason="FAISS may not be available in test environment")
    def test_search_similar_vectors(self, temp_dir):
        """Test searching for similar vectors."""
        storage = FAISSVectorStorage(index_path=temp_dir, dimension=3)
        
        # Store test vectors
        vectors = [
            VectorEntry(
                vector=np.array([1.0, 0.0, 0.0]),
                chunk_id="chunk-1",
                source_file="test1.txt",
            ),
            VectorEntry(
                vector=np.array([0.0, 1.0, 0.0]),
                chunk_id="chunk-2",
                source_file="test2.txt",
            ),
            VectorEntry(
                vector=np.array([0.9, 0.1, 0.0]),  # Similar to first vector
                chunk_id="chunk-3",
                source_file="test3.txt",
            ),
        ]
        
        storage.store_vectors(vectors)
        
        # Search for similar vectors
        query_vector = np.array([1.0, 0.0, 0.0])
        results = storage.search_similar(query_vector, top_k=2)
        
        assert len(results) == 2
        
        # First result should be most similar (chunk-1 or chunk-3)
        best_match, best_score = results[0]
        assert best_match.chunk_id in ["chunk-1", "chunk-3"]
        assert best_score > 0.8  # High similarity
    
    @pytest.mark.skipif(True, reason="FAISS may not be available in test environment")
    def test_save_and_load_index(self, temp_dir):
        """Test saving and loading FAISS index."""
        storage = FAISSVectorStorage(index_path=temp_dir, dimension=3)
        
        # Store test vectors
        vectors = [
            VectorEntry(
                vector=np.array([1.0, 0.0, 0.0]),
                chunk_id="chunk-1",
                source_file="test.txt",
            ),
        ]
        storage.store_vectors(vectors)
        
        # Save index
        success = storage.save_index(temp_dir)
        assert success
        
        # Create new storage and load index
        new_storage = FAISSVectorStorage(index_path=temp_dir, dimension=3)
        load_success = new_storage.load_index(temp_dir)
        
        assert load_success
        assert new_storage.index.ntotal == 1
        assert len(new_storage.metadata_store) == 1
    
    def test_get_index_stats(self, temp_dir):
        """Test getting index statistics."""
        # Mock FAISS to avoid dependency
        with patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', True):
            with patch('ingest.vector_store.faiss') as mock_faiss:
                mock_index = Mock()
                mock_index.ntotal = 5
                mock_faiss.IndexFlatIP.return_value = mock_index
                
                storage = FAISSVectorStorage(index_path=temp_dir, dimension=128)
                storage.metadata_store = [{}] * 5  # Mock metadata
                
                stats = storage.get_index_stats()
                
                assert stats['total_vectors'] == 5
                assert stats['dimension'] == 128
                assert stats['metadata_entries'] == 5
                assert stats['index_path'] == str(temp_dir)


class TestOpenAIEmbedder:
    """Test OpenAI embedding functionality with mocks."""
    
    def test_initialize_embedder_without_libs(self):
        """Test embedder initialization when libraries are not available."""
        with patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', False):
            with pytest.raises(Exception):  # Should raise EmbeddingError
                OpenAIEmbedder()
    
    @patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', True)
    @patch('ingest.vector_store.openai')
    def test_initialize_embedder_with_mocks(self, mock_openai):
        """Test embedder initialization with mocked OpenAI."""
        # Mock OpenAI client and response
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [Mock()]
        mock_response.data[0].embedding = [0.1] * 1536
        mock_client.embeddings.create.return_value = mock_response
        mock_openai.OpenAI.return_value = mock_client
        
        embedder = OpenAIEmbedder(api_key="test-key")
        
        assert embedder.model == "text-embedding-3-small"
        assert embedder.embedding_dimension == 1536
        assert embedder.batch_size == 100
    
    @patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', True)
    @patch('ingest.vector_store.openai')
    def test_embed_text(self, mock_openai):
        """Test text embedding with mocked OpenAI."""
        # Setup mocks
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [Mock()]
        mock_response.data[0].embedding = [0.1, 0.2, 0.3]
        mock_client.embeddings.create.return_value = mock_response
        mock_openai.OpenAI.return_value = mock_client
        
        # Mock the initialization call
        init_response = Mock()
        init_response.data = [Mock()]
        init_response.data[0].embedding = [0.1] * 1536
        mock_client.embeddings.create.side_effect = [init_response, mock_response]
        
        embedder = OpenAIEmbedder(api_key="test-key")
        
        # Test embedding
        result = embedder.embed_text("test text")
        
        assert isinstance(result, np.ndarray)
        assert len(result) == 3
        assert np.allclose(result, [0.1, 0.2, 0.3])
    
    @patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', True)
    @patch('ingest.vector_store.openai')
    def test_embed_batch(self, mock_openai):
        """Test batch text embedding with mocked OpenAI."""
        # Setup mocks
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [Mock(), Mock()]
        mock_response.data[0].embedding = [0.1, 0.2, 0.3]
        mock_response.data[1].embedding = [0.4, 0.5, 0.6]
        mock_client.embeddings.create.return_value = mock_response
        mock_openai.OpenAI.return_value = mock_client
        
        # Mock the initialization call
        init_response = Mock()
        init_response.data = [Mock()]
        init_response.data[0].embedding = [0.1] * 1536
        mock_client.embeddings.create.side_effect = [init_response, mock_response]
        
        embedder = OpenAIEmbedder(api_key="test-key")
        
        # Test batch embedding
        texts = ["text 1", "text 2"]
        results = embedder.embed_batch(texts)
        
        assert len(results) == 2
        assert isinstance(results[0], np.ndarray)
        assert isinstance(results[1], np.ndarray)
        assert np.allclose(results[0], [0.1, 0.2, 0.3])
        assert np.allclose(results[1], [0.4, 0.5, 0.6])


class TestVectorStore:
    """Test high-level vector store functionality."""
    
    @patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', True)
    @patch('ingest.vector_store.openai')
    @patch('ingest.vector_store.faiss')
    def test_initialize_vector_store(self, mock_faiss, mock_openai, temp_dir):
        """Test vector store initialization with mocks."""
        # Mock OpenAI
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [Mock()]
        mock_response.data[0].embedding = [0.1] * 1536
        mock_client.embeddings.create.return_value = mock_response
        mock_openai.OpenAI.return_value = mock_client
        
        # Mock FAISS
        mock_index = Mock()
        mock_index.ntotal = 0
        mock_faiss.IndexFlatIP.return_value = mock_index
        
        store = VectorStore(
            index_path=temp_dir,
            openai_api_key="test-key",
        )
        
        assert store.embedder is not None
        assert store.storage is not None
        assert store.chunker is not None
    
    @patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', True)
    @patch('ingest.vector_store.openai')
    @patch('ingest.vector_store.faiss')
    def test_embed_chunks(self, mock_faiss, mock_openai, temp_dir):
        """Test embedding document chunks."""
        # Setup mocks
        mock_client = Mock()
        
        # Mock initialization response
        init_response = Mock()
        init_response.data = [Mock()]
        init_response.data[0].embedding = [0.1] * 1536
        
        # Mock batch embedding response
        batch_response = Mock()
        batch_response.data = [Mock(), Mock()]
        batch_response.data[0].embedding = [0.1, 0.2, 0.3]
        batch_response.data[1].embedding = [0.4, 0.5, 0.6]
        
        mock_client.embeddings.create.side_effect = [init_response, batch_response]
        mock_openai.OpenAI.return_value = mock_client
        
        # Mock FAISS
        mock_index = Mock()
        mock_index.ntotal = 0
        mock_faiss.IndexFlatIP.return_value = mock_index
        
        store = VectorStore(index_path=temp_dir, openai_api_key="test-key")
        
        # Create test chunks
        chunks = [
            DocumentChunk(
                text="First chunk of text",
                source="test1.txt",
                file_type=FileType.TXT,
                language=LanguageCode.ENGLISH,
            ),
            DocumentChunk(
                text="Second chunk of text",
                source="test2.txt",
                file_type=FileType.TXT,
                language=LanguageCode.ENGLISH,
            ),
        ]
        
        # Test embedding
        vector_entries = store.embed_chunks(chunks)
        
        assert len(vector_entries) == 2
        assert all(isinstance(entry, VectorEntry) for entry in vector_entries)
        assert vector_entries[0].chunk_id == chunks[0].id
        assert vector_entries[1].chunk_id == chunks[1].id
    
    def test_vector_store_without_dependencies(self):
        """Test vector store behavior when dependencies are missing."""
        with patch('ingest.vector_store.VECTOR_LIBS_AVAILABLE', False):
            with pytest.raises(Exception):  # Should raise an error
                VectorStore()
