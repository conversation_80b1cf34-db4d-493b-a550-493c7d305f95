"""
Prequalification Question Bank schemas
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
from app.schemas.base_response import ResponseMessage


class PreQualificationQuestionResponse(BaseModel):
    """Response model for pre-qualification questions"""
    id: str = Field(..., description="Question ID", example="550e8400-e29b-41d4-a716-************")
    name: Optional[str] = Field(None, description="Question name", example="What is your investment budget?")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    expected_answers: Optional[List[Any]] = Field(None, description="Expected answers as JSON array")
    question_id: Optional[str] = Field(None, description="Question identifier", example="Q001")
    category: Optional[str] = Field(None, description="Question category", example="Financial")
    score_weight: Optional[int] = Field(None, description="Score weight for the question")
    context_info: Optional[Dict[str, Any]] = Field(None, description="Additional context information")
    qualification_weight: Optional[float] = Field(None, description="Qualification weight")
    expected_answer_type: Optional[str] = Field(None, description="Expected answer type", example="multiple_choice")
    answer_options: Optional[str] = Field(None, description="Available answer options")
    passing_criteria: Optional[str] = Field(None, description="Criteria for passing")
    validation_rules: Optional[str] = Field(None, description="Validation rules")
    requires_follow_up: Optional[bool] = Field(None, description="Whether follow-up is required")
    follow_up_logic: Optional[str] = Field(None, description="Follow-up logic")
    is_deleted: Optional[bool] = Field(None, description="Whether the question is deleted")
    is_active: Optional[bool] = Field(None, description="Whether the question is active")
    is_required: Optional[bool] = Field(None, description="Whether the question is required")
    order_sequence: Optional[int] = Field(None, description="Order sequence")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")

    class Config:
        from_attributes = True


class QuestionBankResponse(BaseModel):
    """Response model for question bank (legacy)"""
    id: str = Field(..., description="Question bank ID", example="550e8400-e29b-41d4-a716-************")
    name: str = Field(..., description="Question name", example="What is your investment budget?")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    lead_first_name: Optional[str] = Field(None, description="First name of the associated lead")
    lead_last_name: Optional[str] = Field(None, description="Last name of the associated lead")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    franchisor_name: Optional[str] = Field(None, description="Name of the associated franchisor")
    is_deleted: bool = Field(..., description="Whether the question is deleted")
    is_active: bool = Field(..., description="Whether the question is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


class PreQualificationQuestionListResponse(BaseModel):
    """Response model for pre-qualification question list"""
    items: List[PreQualificationQuestionResponse] = Field(..., description="List of pre-qualification questions")
    total_count: int = Field(..., description="Total number of items")


class PreQualificationQuestionSuccessResponse(BaseModel):
    """Standard success response for pre-qualification question operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: PreQualificationQuestionListResponse = Field(..., description="Pre-qualification question data")


class QuestionBankListResponse(BaseModel):
    """Response model for question bank list (legacy)"""
    items: List[QuestionBankResponse] = Field(..., description="List of question bank items")
    total_count: int = Field(..., description="Total number of items")
    page: Optional[int] = Field(None, description="Current page number")
    size: Optional[int] = Field(None, description="Number of items per page")
    total_pages: Optional[int] = Field(None, description="Total number of pages")


class QuestionBankSuccessResponse(BaseModel):
    """Standard success response for question bank operations (legacy)"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: QuestionBankListResponse = Field(..., description="Question bank data")


class EscalationQuestionBankResponse(BaseModel):
    """Response model for escalation question bank"""
    id: str = Field(..., description="Escalation question bank ID", example="550e8400-e29b-41d4-a716-************")
    name: str = Field(..., description="Question name", example="Budget inquiry escalation")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    lead_first_name: Optional[str] = Field(None, description="First name of the associated lead")
    lead_last_name: Optional[str] = Field(None, description="Last name of the associated lead")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    franchisor_name: Optional[str] = Field(None, description="Name of the associated franchisor")
    answer: Optional[List[str]] = Field(None, description="Array of answer strings", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])
    support_status: Optional[str] = Field(None, description="Support status", example="Pending review")
    is_deleted: bool = Field(..., description="Whether the question is deleted")
    is_active: bool = Field(..., description="Whether the question is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


class EscalationQuestionBankListResponse(BaseModel):
    """Response model for escalation question bank list"""
    items: List[EscalationQuestionBankResponse] = Field(..., description="List of escalation question bank items")
    total_count: int = Field(..., description="Total number of items")
    page: Optional[int] = Field(None, description="Current page number")
    size: Optional[int] = Field(None, description="Number of items per page")
    total_pages: Optional[int] = Field(None, description="Total number of pages")


class EscalationQuestionBankSuccessResponse(BaseModel):
    """Standard success response for escalation question bank operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: EscalationQuestionBankListResponse = Field(..., description="Escalation question bank data")


class EscalationAnswerUpdateRequest(BaseModel):
    """Request model for updating escalation question answer"""
    answer: List[str] = Field(..., description="Array of answer strings", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])

    class Config:
        from_attributes = True


class EscalationStatusUpdateRequest(BaseModel):
    """Request model for updating escalation question support status"""
    support_status: str = Field(..., description="Support status", example="Resolved")

    class Config:
        from_attributes = True


class EscalationUpdateSuccessResponse(BaseModel):
    """Standard success response for escalation question update operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: EscalationQuestionBankResponse = Field(..., description="Updated escalation question data")
