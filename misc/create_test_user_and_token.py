#!/usr/bin/env python3
"""
Create Test User and Generate Authentication Token
Creates a test user and generates a JWT token for testing the PDF upload functionality
"""

import asyncio
import sys
from pathlib import Path
import uuid
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def create_test_user():
    """Create a test user in the database"""
    try:
        print("👤 Creating test user...")
        
        # Import required modules
        from app.core.database.connection import get_db
        from app.models.user import User
        from app.api.v1.endpoints.auth import get_password_hash
        from sqlalchemy import select
        
        # Test user data
        test_email = "<EMAIL>"
        test_password = "TestPassword123!"
        
        # Get database session
        async for db in get_db():
            # Check if user already exists
            result = await db.execute(select(User).where(User.email == test_email))
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print(f"✅ Test user already exists: {test_email}")
                return existing_user
            
            # Create new test user
            hashed_password = get_password_hash(test_password)
            
            new_user = User(
                id=uuid.uuid4(),
                email=test_email,
                password_hash=hashed_password,
                first_name="Test",
                last_name="User",
                role="ADMIN",
                is_active=True,
                is_deleted=False,
                is_email_verified=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            
            print("✅ Test user created successfully:")
            print(f"   - Email: {test_email}")
            print(f"   - Password: {test_password}")
            print(f"   - User ID: {new_user.id}")
            print(f"   - Role: {new_user.role}")
            
            return new_user
            
    except Exception as e:
        print(f"❌ Error creating test user: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def generate_auth_token(user):
    """Generate authentication token for the test user"""
    try:
        print("\n🔑 Generating authentication token...")
        
        # Import JWT handler
        from app.core.security.authentication_manager.jwt_handler import JWTHandler
        
        # Create JWT handler
        jwt_handler = JWTHandler()
        
        # Prepare token data
        token_data = {
            "user_id": str(user.id),
            "email": user.email
        }
        
        # Generate access token (long-lived for testing)
        access_token = jwt_handler.create_token(
            data=token_data,
            token_type="access",
            remember_me=True  # Long-lived token for testing
        )
        
        print("✅ Authentication token generated successfully:")
        print(f"   - Token: {access_token}")
        print(f"   - User ID: {user.id}")
        print(f"   - Email: {user.email}")
        
        return access_token
        
    except Exception as e:
        print(f"❌ Error generating token: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def test_token_validation(token):
    """Test token validation"""
    try:
        print("\n🔍 Testing token validation...")
        
        # Import JWT handler
        from app.core.security.authentication_manager.jwt_handler import JWTHandler
        
        # Create JWT handler
        jwt_handler = JWTHandler()
        
        # Decode and validate token
        decoded_payload = await jwt_handler.decode_token(token)
        
        print("✅ Token validation successful:")
        print(f"   - User ID: {decoded_payload.get('user_id')}")
        print(f"   - Email: {decoded_payload.get('email')}")
        print(f"   - Token Type: {decoded_payload.get('type')}")
        print(f"   - Expires: {datetime.fromtimestamp(decoded_payload.get('exp'))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token validation failed: {str(e)}")
        return False


async def save_token_to_file(token, user):
    """Save token and user info to a file for easy access"""
    try:
        token_file = "test_auth_token.txt"
        
        with open(token_file, 'w') as f:
            f.write("# Test Authentication Token for GrowthHive\n")
            f.write(f"# Generated: {datetime.now().isoformat()}\n\n")
            f.write(f"EMAIL={user.email}\n")
            f.write("PASSWORD=TestPassword123!\n")
            f.write(f"USER_ID={user.id}\n")
            f.write(f"ACCESS_TOKEN={token}\n\n")
            f.write("# Use this token in Authorization header:\n")
            f.write(f"# Authorization: Bearer {token}\n")
        
        print(f"\n💾 Token saved to file: {token_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error saving token to file: {str(e)}")
        return False


async def main():
    """Main function"""
    print("🚀 Creating Test User and Authentication Token")
    print("=" * 50)
    
    try:
        # Step 1: Create test user
        user = await create_test_user()
        if not user:
            print("❌ Failed to create test user")
            return False
        
        # Step 2: Generate authentication token
        token = await generate_auth_token(user)
        if not token:
            print("❌ Failed to generate authentication token")
            return False
        
        # Step 3: Test token validation
        token_valid = await test_token_validation(token)
        if not token_valid:
            print("❌ Token validation failed")
            return False
        
        # Step 4: Save token to file
        await save_token_to_file(token, user)
        
        print("\n" + "=" * 50)
        print("🎉 SUCCESS! Test user and token created successfully")
        print("=" * 50)
        
        print("\n📋 Quick Reference:")
        print(f"Email: {user.email}")
        print("Password: TestPassword123!")
        print(f"Token: {token[:50]}...")
        
        print("\n🧪 Ready for testing!")
        print("You can now use this token to test the PDF upload functionality.")
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Operation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)
