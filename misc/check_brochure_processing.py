#!/usr/bin/env python3
"""
Check Brochure Processing Status
Checks if brochures are currently being processed and shows their status
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database.connection import get_db
from sqlalchemy import text
from app.core.logging import logger

async def check_document_processing_status():
    """Check current document processing status"""
    try:
        async for db in get_db():
            # Query documents with processing status
            query = text("""
                SELECT
                    id,
                    name,
                    file_type,
                    processing_status,
                    processing_progress,
                    processing_message,
                    processing_error,
                    processing_started_at,
                    processing_completed_at,
                    created_at,
                    updated_at
                FROM documents
                WHERE is_deleted = false
                AND (processing_status != 'pending' OR processing_started_at IS NOT NULL)
                ORDER BY processing_started_at DESC NULLS LAST, created_at DESC
                LIMIT 20
            """)
            
            result = await db.execute(query)
            documents = result.fetchall()
            
            if not documents:
                print("📄 No documents found with processing activity")
                return
            
            print(f"📄 Found {len(documents)} documents with processing activity:")
            print("=" * 100)
            
            for doc in documents:
                status_emoji = {
                    'pending': '⏳',
                    'processing': '🔄',
                    'processed': '✅',
                    'failed': '❌'
                }.get(doc.processing_status, '❓')
                
                print(f"{status_emoji} {doc.filename} ({doc.file_type})")
                print(f"   ID: {doc.id}")
                print(f"   Status: {doc.processing_status}")
                if doc.processing_progress is not None:
                    print(f"   Progress: {doc.processing_progress}%")
                if doc.processing_message:
                    print(f"   Message: {doc.processing_message}")
                if doc.processing_error:
                    print(f"   Error: {doc.processing_error}")
                if doc.processing_started_at:
                    print(f"   Started: {doc.processing_started_at}")
                if doc.processing_completed_at:
                    print(f"   Completed: {doc.processing_completed_at}")
                print(f"   Created: {doc.created_at}")
                print("-" * 50)
            
            break
            
    except Exception as e:
        logger.error(f"Error checking document processing status: {e}")
        print(f"❌ Error: {e}")

async def check_franchisor_brochures():
    """Check franchisor brochures and their processing status"""
    try:
        async for db in get_db():
            # Query franchisors with brochure information
            query = text("""
                SELECT 
                    id,
                    name,
                    brochure_url,
                    embedding,
                    created_at,
                    updated_at
                FROM franchisors 
                WHERE is_deleted = false 
                AND brochure_url IS NOT NULL
                ORDER BY updated_at DESC
                LIMIT 10
            """)
            
            result = await db.execute(query)
            franchisors = result.fetchall()
            
            if not franchisors:
                print("🏢 No franchisors found with brochures")
                return
            
            print(f"🏢 Found {len(franchisors)} franchisors with brochures:")
            print("=" * 100)
            
            for franchisor in franchisors:
                embedding_status = "✅ Has embedding" if franchisor.embedding else "❌ No embedding"
                
                print(f"🏢 {franchisor.name}")
                print(f"   ID: {franchisor.id}")
                print(f"   Brochure URL: {franchisor.brochure_url}")
                print(f"   Embedding: {embedding_status}")
                print(f"   Updated: {franchisor.updated_at}")
                print("-" * 50)
            
            break
            
    except Exception as e:
        logger.error(f"Error checking franchisor brochures: {e}")
        print(f"❌ Error: {e}")

async def check_document_chunks():
    """Check document chunks for processed documents"""
    try:
        async for db in get_db():
            # Query document chunks
            query = text("""
                SELECT 
                    dc.id,
                    dc.document_id,
                    d.filename,
                    dc.text,
                    dc.embedding,
                    dc.metadata,
                    dc.created_at
                FROM document_chunks dc
                JOIN documents d ON dc.document_id = d.id
                WHERE d.is_deleted = false
                ORDER BY dc.created_at DESC
                LIMIT 10
            """)
            
            result = await db.execute(query)
            chunks = result.fetchall()
            
            if not chunks:
                print("📝 No document chunks found")
                return
            
            print(f"📝 Found {len(chunks)} recent document chunks:")
            print("=" * 100)
            
            for chunk in chunks:
                embedding_status = "✅ Has embedding" if chunk.embedding else "❌ No embedding"
                text_preview = chunk.text[:100] + "..." if len(chunk.text) > 100 else chunk.text
                
                print(f"📝 Chunk from: {chunk.filename}")
                print(f"   Chunk ID: {chunk.id}")
                print(f"   Document ID: {chunk.document_id}")
                print(f"   Text Preview: {text_preview}")
                print(f"   Embedding: {embedding_status}")
                print(f"   Created: {chunk.created_at}")
                print("-" * 50)
            
            break
            
    except Exception as e:
        logger.error(f"Error checking document chunks: {e}")
        print(f"❌ Error: {e}")

async def check_celery_tasks():
    """Check Celery task status"""
    try:
        # Try to import Celery and check active tasks
        from app.core.celery_app import celery_app
        
        # Get active tasks
        active_tasks = celery_app.control.active()
        
        if not active_tasks:
            print("🔄 No active Celery tasks found")
            return
        
        print("🔄 Active Celery tasks:")
        print("=" * 100)
        
        for worker, tasks in active_tasks.items():
            print(f"Worker: {worker}")
            for task in tasks:
                print(f"   Task: {task.get('name', 'Unknown')}")
                print(f"   ID: {task.get('id', 'Unknown')}")
                print(f"   Args: {task.get('args', [])}")
                print(f"   Started: {task.get('time_start', 'Unknown')}")
                print("-" * 30)
        
    except Exception as e:
        print(f"⚠️  Could not check Celery tasks: {e}")

async def main():
    """Main function to check all brochure processing status"""
    print("🔍 GrowthHive Brochure Processing Status Check")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Check document processing status
    print("1️⃣ Checking Document Processing Status...")
    await check_document_processing_status()
    print()
    
    # Check franchisor brochures
    print("2️⃣ Checking Franchisor Brochures...")
    await check_franchisor_brochures()
    print()
    
    # Check document chunks
    print("3️⃣ Checking Document Chunks...")
    await check_document_chunks()
    print()
    
    # Check Celery tasks
    print("4️⃣ Checking Celery Tasks...")
    await check_celery_tasks()
    print()
    
    print("✅ Status check completed!")

if __name__ == "__main__":
    asyncio.run(main())
