#!/usr/bin/env python3
"""
Test that industry filtering + sorting works without duplicate JOIN error
"""

import asyncio
import httpx
import uuid

BASE_URL = "http://localhost:8000"

async def test_industry_sort_fix():
    async with httpx.AsyncClient() as client:
        # Register and login
        user_email = f"test_{str(uuid.uuid4())[:8]}@example.com"
        register_data = {
            "email": user_email,
            "password": "Test123!",
            "confirm_password": "Test123!",
            "first_name": "Test",
            "last_name": "User",
            "mobile": f"+{str(uuid.uuid4().int)[:10]}"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code != 201:
            print(f"❌ Registration failed: {response.status_code}")
            return
        
        login_data = {
            "email_or_mobile": user_email,
            "password": "Test123!"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            return
        
        data = response.json()
        access_token = data["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        print("✅ Login successful")
        
        # Test the combination that was causing duplicate JOIN error
        test_cases = [
            {"industry": "food", "sort_by": "industry_name", "sort_order": "desc"},
            {"industry": "food", "sort_by": "name", "sort_order": "asc"},
            {"industry": "07a64d9b-1f6f-4fe4-aa76-84de693d2e06", "sort_by": "industry_name", "sort_order": "asc"},
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. Testing: {test_case}")
            
            response = await client.get(
                f"{BASE_URL}/api/franchisors/",
                params={**test_case, "limit": 3},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                items = data.get("data", {}).get("items", [])
                print(f"   ✅ Success: {len(items)} franchisors returned")
                
                # Show first result
                if items:
                    first = items[0]
                    industry_name = first.get("industry_details", {}).get("name", "N/A")
                    print(f"   📋 First result: {first['name']} (Industry: {industry_name})")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                if response.status_code == 500:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('message', 'Unknown error')}")
        
        print("\n✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(test_industry_sort_fix())
