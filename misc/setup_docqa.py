#!/usr/bin/env python3
"""
Setup script for DocQA system
"""

import os
import sys
import subprocess
from pathlib import Path
import psycopg

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 10):
        print("❌ Python 3.10+ is required")
        return False
    
    # Check OpenAI API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY environment variable is required")
        return False
    
    # Check database URL
    if not os.getenv("DATABASE_URL"):
        print("❌ DATABASE_URL environment variable is required")
        return False
    
    print("✅ Requirements check passed")
    return True


def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    requirements_file = Path("docqa/requirements.txt")
    if not requirements_file.exists():
        print("❌ Requirements file not found")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False


def setup_database():
    """Setup database with pgvector"""
    print("🗄️  Setting up database...")
    
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL not set")
        return False
    
    try:
        # Connect to database
        with psycopg.connect(database_url) as conn:
            with conn.cursor() as cur:
                # Read and execute setup SQL
                setup_sql = Path("docqa/setup_pgvector.sql")
                if not setup_sql.exists():
                    print("❌ Setup SQL file not found")
                    return False
                
                sql_content = setup_sql.read_text()
                cur.execute(sql_content)
                conn.commit()
                
                print("✅ Database setup completed")
                return True
                
    except Exception as e:
        print(f"❌ Database setup failed: {str(e)}")
        return False


def test_system():
    """Test the DocQA system"""
    print("🧪 Testing system...")
    
    try:
        # Import and test basic functionality
        from docqa.serve import health_check
        
        health = health_check()
        if health['status'] == 'healthy':
            print("✅ System test passed")
            return True
        else:
            print(f"❌ System test failed: {health.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ System test failed: {str(e)}")
        return False


def main():
    """Main setup function"""
    print("🚀 DocQA Setup Script")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Setup failed: Requirements not met")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed: Could not install dependencies")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("\n❌ Setup failed: Database setup failed")
        sys.exit(1)
    
    # Test system
    if not test_system():
        print("\n❌ Setup failed: System test failed")
        sys.exit(1)
    
    print("\n🎉 DocQA setup completed successfully!")
    print("\nNext steps:")
    print("1. Ingest some documents:")
    print("   python -m docqa.cli ingest s3://your-bucket/document.pdf")
    print("2. Ask questions:")
    print("   python -m docqa.cli ask 'What franchise opportunities are available?'")
    print("3. Check status:")
    print("   python -m docqa.cli status")


if __name__ == "__main__":
    main()
