#!/usr/bin/env python3
"""
Database Fix Script for conversation_message table
Uses asyncpg to connect and fix the missing updated_at column
"""

import asyncio
import asyncpg
import os
from typing import Optional

async def fix_conversation_message_table():
    """Fix the conversation_message table by adding missing updated_at column"""
    
    print("🔧 Running Database Fix for conversation_message table")
    print("=" * 60)
    
    # Database connection details from .env file
    DATABASE_URL = "postgresql://postgres:root@localhost:5432/growthhive"
    
    try:
        # Connect to database
        print("📊 Connecting to database...")
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connected successfully!")
        
        # Check if conversation_message table exists
        print("\n🔍 Checking if conversation_message table exists...")
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'conversation_message'
            )
        """)
        
        if not table_exists:
            print("❌ conversation_message table does not exist!")
            print("💡 Please run the migration first: alembic upgrade head")
            return
        
        print("✅ conversation_message table exists!")
        
        # Check current table structure
        print("\n📋 Current table structure:")
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'conversation_message' 
            ORDER BY ordinal_position
        """)
        
        for col in columns:
            print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
        
        # Check if updated_at column exists
        updated_at_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_name = 'conversation_message' 
                AND column_name = 'updated_at'
            )
        """)
        
        if not updated_at_exists:
            print("\n🔧 Adding missing updated_at column...")
            await conn.execute("""
                ALTER TABLE conversation_message 
                ADD COLUMN updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
            """)
            print("✅ Added updated_at column!")
        else:
            print("\n✅ updated_at column already exists!")
        
        # Add index on updated_at
        print("\n📊 Adding index on updated_at column...")
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS ix_conversation_message_updated_at 
            ON conversation_message(updated_at)
        """)
        print("✅ Added index on updated_at!")
        
        # Update existing records
        print("\n🔄 Updating existing records...")
        updated_count = await conn.execute("""
            UPDATE conversation_message 
            SET updated_at = created_at 
            WHERE updated_at IS NULL
        """)
        print(f"✅ Updated {updated_count} records!")
        
        # Verify the fix
        print("\n✅ Verifying the fix...")
        updated_at_info = await conn.fetchrow("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'conversation_message' 
            AND column_name = 'updated_at'
        """)
        
        if updated_at_info:
            print(f"✅ updated_at column verified:")
            print(f"  - Name: {updated_at_info['column_name']}")
            print(f"  - Type: {updated_at_info['data_type']}")
            print(f"  - Nullable: {updated_at_info['is_nullable']}")
            print(f"  - Default: {updated_at_info['column_default']}")
        
        # Test insert to verify the fix works
        print("\n🧪 Testing insert to verify fix...")
        test_message_id = await conn.fetchval("""
            INSERT INTO conversation_message (
                id, lead_id, franchisor_id, sender, message, 
                is_active, is_deleted, created_at, updated_at
            ) VALUES (
                gen_random_uuid(), 
                '85ec9a9d-aeae-4d5c-ba84-376f261f8b3c'::uuid, 
                '569976f2-d845-4615-8a91-96e18086adbe'::uuid, 
                'lead', 
                'Test message to verify fix', 
                true, 
                false, 
                now(), 
                now()
            ) RETURNING id
        """)
        
        print(f"✅ Test message inserted successfully with ID: {test_message_id}")
        
        # Clean up test message
        await conn.execute("DELETE FROM conversation_message WHERE id = $1", test_message_id)
        print("✅ Test message cleaned up successfully!")
        
        print("\n🎉 Database fix completed successfully!")
        print("📋 You can now test the webhook conversation storage functionality.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure PostgreSQL is running and accessible")
        
    finally:
        if 'conn' in locals():
            await conn.close()

async def main():
    """Main function"""
    await fix_conversation_message_table()

if __name__ == "__main__":
    asyncio.run(main()) 