#!/bin/bash
"""
GrowthHive Complete Startup Script
Starts all required services and the application
"""

set -e

echo "🚀 Starting GrowthHive Application with All Services"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker Desktop first."
    exit 1
fi
print_status "Docker is running"

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create it with required environment variables."
    exit 1
fi
print_status ".env file found"

# Step 1: Stop any existing containers
print_info "Stopping any existing containers..."
docker-compose -f docker-compose.rabbitmq.yml down --remove-orphans 2>/dev/null || true

# Step 2: Start PostgreSQL Database
print_info "Starting PostgreSQL database..."
docker-compose -f docker-compose.rabbitmq.yml up -d postgres
sleep 5

# Wait for PostgreSQL to be ready
print_info "Waiting for PostgreSQL to be ready..."
until docker-compose -f docker-compose.rabbitmq.yml exec -T postgres pg_isready -U growthhive -d growthhive; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done
print_status "PostgreSQL is ready"

# Step 3: Start RabbitMQ
print_info "Starting RabbitMQ..."
docker-compose -f docker-compose.rabbitmq.yml up -d rabbitmq
sleep 10

# Wait for RabbitMQ to be ready
print_info "Waiting for RabbitMQ to be ready..."
until docker-compose -f docker-compose.rabbitmq.yml exec -T rabbitmq rabbitmqctl status > /dev/null 2>&1; do
    echo "Waiting for RabbitMQ..."
    sleep 3
done
print_status "RabbitMQ is ready"

# Step 4: Start Redis
print_info "Starting Redis..."
docker-compose -f docker-compose.rabbitmq.yml up -d redis
sleep 3

# Wait for Redis to be ready
print_info "Waiting for Redis to be ready..."
until docker-compose -f docker-compose.rabbitmq.yml exec -T redis redis-cli ping > /dev/null 2>&1; do
    echo "Waiting for Redis..."
    sleep 2
done
print_status "Redis is ready"

# Step 5: Activate Python virtual environment
print_info "Activating Python virtual environment..."
if [ ! -d "venv311" ]; then
    print_error "Virtual environment 'venv311' not found. Please create it first."
    exit 1
fi

source venv311/bin/activate
print_status "Virtual environment activated"

# Step 6: Install/Update Python dependencies
print_info "Installing Python dependencies..."
pip install -q -r requirements.txt
pip install -q langgraph langgraph-checkpoint langchain langchain-openai langchain-community langchain-core phonenumbers
print_status "Python dependencies installed"

# Step 7: Run database migrations (if any)
print_info "Running database migrations..."
# Add your migration commands here if needed
# python manage.py migrate
print_status "Database migrations completed"

# Step 8: Start Celery Worker in background
print_info "Starting Celery worker..."
nohup python celery_worker.py --loglevel=info --concurrency=4 --queues=document_processing > logs/celery_worker.log 2>&1 &
CELERY_PID=$!
sleep 3

# Check if Celery worker started successfully
if ps -p $CELERY_PID > /dev/null; then
    print_status "Celery worker started (PID: $CELERY_PID)"
else
    print_error "Failed to start Celery worker"
    exit 1
fi

# Step 9: Start FastAPI application
print_info "Starting FastAPI application..."
print_info "Server will be available at: http://localhost:8000"
print_info "API Documentation: http://localhost:8000/api/docs"
print_info "RabbitMQ Management: http://localhost:15672 (growthhive/growthhive123)"
print_info ""
print_status "All services started successfully!"
print_info ""
print_warning "Press Ctrl+C to stop all services"
print_info ""

# Function to cleanup on exit
cleanup() {
    echo ""
    print_info "Shutting down services..."
    
    # Stop Celery worker
    if ps -p $CELERY_PID > /dev/null 2>/dev/null; then
        kill $CELERY_PID
        print_status "Celery worker stopped"
    fi
    
    # Stop Docker containers
    docker-compose -f docker-compose.rabbitmq.yml down
    print_status "Docker containers stopped"
    
    print_status "All services stopped successfully"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start FastAPI server (this will block until Ctrl+C)
python start_server.py
