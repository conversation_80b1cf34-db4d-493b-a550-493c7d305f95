#!/usr/bin/env python3
"""
Test script for Franchisor module changes:
1. Test franchisor_won_id field
2. Test Zoho sync (pull only for franchisors)
3. Test franchisor listing with sorting
"""

import asyncio
import httpx
import json
import sys
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

class FranchisorTester:
    def __init__(self):
        self.access_token = None
        self.client = httpx.AsyncClient()
    
    async def login(self, email: str = "<EMAIL>", password: str = "password123"):
        """Login and get access token"""
        try:
            response = await self.client.post(
                f"{BASE_URL}/api/auth/login",
                json={"email_or_mobile": email, "password": password}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data["data"]["access_token"]
                print("✅ Login successful")
                return True
            else:
                print(f"❌ Login failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    def get_headers(self):
        """Get headers with authorization"""
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
    
    async def test_franchisor_listing_with_sorting(self):
        """Test franchisor listing with different sorting options"""
        print("\n🔍 Testing Franchisor Listing with Sorting...")
        
        sort_tests = [
            {"sort_by": "name", "sort_order": "asc"},
            {"sort_by": "name", "sort_order": "desc"},
            {"sort_by": "contactfirstname", "sort_order": "asc"},
            {"sort_by": "contactlastname", "sort_order": "asc"},
            {"sort_by": "franchisor_won_id", "sort_order": "asc"},
            {"sort_by": "industry_name", "sort_order": "asc"},
            {"sort_by": "is_active", "sort_order": "desc"},
        ]
        
        for test in sort_tests:
            try:
                params = {
                    "limit": 10,
                    "skip": 0,
                    **test
                }
                
                response = await self.client.get(
                    f"{API_BASE}/franchisors/",
                    params=params,
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    items = data.get("data", {}).get("items", [])
                    print(f"✅ Sort by {test['sort_by']} ({test['sort_order']}): {len(items)} items")
                    
                    # Show first few items to verify sorting
                    if items:
                        print(f"   First item: {items[0].get('name', 'N/A')} - {items[0].get(test['sort_by'], 'N/A')}")
                        if len(items) > 1:
                            print(f"   Second item: {items[1].get('name', 'N/A')} - {items[1].get(test['sort_by'], 'N/A')}")
                else:
                    print(f"❌ Sort test failed for {test['sort_by']}: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error testing sort by {test['sort_by']}: {e}")
    
    async def test_franchisor_response_structure(self):
        """Test that franchisor response includes franchisor_won_id"""
        print("\n🔍 Testing Franchisor Response Structure...")
        
        try:
            response = await self.client.get(
                f"{API_BASE}/franchisors/",
                params={"limit": 5},
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                items = data.get("data", {}).get("items", [])
                
                if items:
                    first_item = items[0]
                    required_fields = [
                        "id", "name", "contactFirstName", "contactLastName", 
                        "email", "phone", "franchisor_won_id", "is_active"
                    ]
                    
                    missing_fields = []
                    for field in required_fields:
                        if field not in first_item:
                            missing_fields.append(field)
                    
                    if not missing_fields:
                        print("✅ All required fields present in response")
                        print(f"   franchisor_won_id: {first_item.get('franchisor_won_id', 'null')}")
                    else:
                        print(f"❌ Missing fields: {missing_fields}")
                else:
                    print("⚠️ No franchisors found to test response structure")
            else:
                print(f"❌ Failed to get franchisors: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing response structure: {e}")
    
    async def test_zoho_sync(self):
        """Test Zoho sync functionality"""
        print("\n🔍 Testing Zoho Sync...")
        
        try:
            response = await self.client.post(
                f"{API_BASE}/zoho/sync",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Zoho sync completed successfully")
                
                # Check sync results
                sync_data = data.get("data", {})
                if "franchisors_pulled" in sync_data:
                    print(f"   Franchisors pulled: {sync_data.get('franchisors_pulled', 0)}")
                if "franchisors_updated" in sync_data:
                    print(f"   Franchisors updated: {sync_data.get('franchisors_updated', 0)}")
                if "leads_pulled" in sync_data:
                    print(f"   Leads pulled: {sync_data.get('leads_pulled', 0)}")
                if "leads_pushed" in sync_data:
                    print(f"   Leads pushed: {sync_data.get('leads_pushed', 0)}")
                    
            else:
                print(f"❌ Zoho sync failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing Zoho sync: {e}")
    
    async def test_search_functionality(self):
        """Test search functionality still works"""
        print("\n🔍 Testing Search Functionality...")
        
        try:
            response = await self.client.get(
                f"{API_BASE}/franchisors/",
                params={"search": "test", "limit": 5},
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                items = data.get("data", {}).get("items", [])
                print(f"✅ Search functionality working: {len(items)} results for 'test'")
            else:
                print(f"❌ Search test failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing search: {e}")
    
    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Franchisor Module Tests...")
        
        # Login first
        if not await self.login():
            print("❌ Cannot proceed without login")
            return
        
        # Run tests
        await self.test_franchisor_response_structure()
        await self.test_franchisor_listing_with_sorting()
        await self.test_search_functionality()
        await self.test_zoho_sync()
        
        print("\n✅ All tests completed!")
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

async def main():
    """Main test function"""
    tester = FranchisorTester()
    try:
        await tester.run_all_tests()
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
