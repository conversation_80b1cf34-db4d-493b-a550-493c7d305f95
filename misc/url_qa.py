#!/usr/bin/env python3
"""
URL-based Document Q&A System

Specify a document URL, and ask questions about it.
Clean interface with no screen blinking.
"""

import os
import subprocess
import argparse


class URLDocumentQA:
    """Simple URL-based document Q&A system."""
    
    def __init__(self):
        self.document_processed = False
        self.document_url = None
    
    def process_document(self, url):
        """Process document from URL."""
        print("📄 Processing document from URL...")
        print(f"🔗 URL: {url}")
        
        # Convert HTTPS URL to S3 format if needed
        if url.startswith('https://openxcell-development-public.s3.ap-south-1.amazonaws.com/'):
            # Convert to S3 format
            s3_path = url.replace('https://openxcell-development-public.s3.ap-south-1.amazonaws.com/', '')
            s3_url = f"s3://openxcell-development-public/{s3_path}"
            print(f"🔄 Converting to S3 format: {s3_url}")
            url = s3_url
        
        try:
            # Ingest the document
            result = subprocess.run([
                "python", "docqa.py", "ingest", url, "--force"
            ], capture_output=True, text=True, env={**os.environ})
            
            if result.returncode == 0:
                print("✅ Document processed successfully!")
                print("📊 Ready for questions.")
                self.document_processed = True
                self.document_url = url
                return True
            else:
                print("❌ Failed to process document")
                print(f"Error: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error processing document: {e}")
            return False
    
    def ask_question(self, question):
        """Ask a question about the processed document."""
        if not self.document_processed:
            return "❌ No document has been processed yet."
        
        try:
            result = subprocess.run([
                "python", "docqa.py", "ask", question, "--no-stream"
            ], capture_output=True, text=True, env={**os.environ})
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return f"❌ Error: {result.stderr.strip()}"
                
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def interactive_qa(self):
        """Start interactive Q&A session."""
        print("\n🤖 Document Q&A System")
        print(f"📄 Document: {self.document_url}")
        print("💡 Ask questions about the document. Type 'quit' to exit.")
        print("=" * 60)
        
        while True:
            try:
                question = input("\n❓ Your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if not question:
                    continue
                
                print("\n💭 Answer:")
                answer = self.ask_question(question)
                print(answer)
                print("\n" + "-" * 50)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="URL-based Document Q&A System")
    parser.add_argument("url", nargs='?', help="Document URL to process")
    parser.add_argument("--question", "-q", help="Single question to ask")
    
    args = parser.parse_args()
    
    # Check for OpenAI API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Initialize system
    qa_system = URLDocumentQA()
    
    # Get URL
    if args.url:
        url = args.url
    else:
        # Default to the franchise document
        url = "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/brochure/20250701_113703_07ce376fa750.pdf"
        print("📄 Using default document URL:")
        print(f"🔗 {url}")
    
    # Process document
    if not qa_system.process_document(url):
        return
    
    # Handle single question or interactive mode
    if args.question:
        # Single question mode
        print(f"\n❓ Question: {args.question}")
        print("\n💭 Answer:")
        answer = qa_system.ask_question(args.question)
        print(answer)
    else:
        # Interactive mode
        qa_system.interactive_qa()


if __name__ == "__main__":
    main()
