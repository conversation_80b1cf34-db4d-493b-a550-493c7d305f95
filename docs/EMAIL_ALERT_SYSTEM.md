# 🚨 GrowthHive Enhanced Email Alert System

A robust, user-friendly, production-ready email alert system for monitoring critical events, errors, and security incidents in the GrowthHive application with comprehensive context and beautiful formatting.

## 🏗️ Architecture Overview

The enhanced email alert system is built with the following components:

- **EmailAlertService**: Core service for sending alerts with rich formatting
- **Email Alert Utilities**: Convenient wrapper functions for common alert types
- **Global Exception Handler Integration**: Automatic alerts with full request context
- **Rate Limiting**: Prevents alert spam with intelligent cooldown
- **Retry Logic**: Ensures reliable delivery with exponential backoff
- **Multiple Alert Types**: Categorized alerts with severity indicators
- **Rich Context Collection**: Comprehensive request, user, and system information
- **HTML Email Support**: Beautiful, readable email formatting
- **Security-Conscious**: Automatic filtering of sensitive information

## 📧 Configuration

### Environment Variables

Add the following to your `.env` file:

```env
# Email Alert Configuration
EMAIL_ALERTS_ENABLED=true
ALERT_EMAIL_SENDER=<EMAIL>
ALERT_EMAIL_PASSWORD=illk owms hody uaqw
ALERT_EMAIL_RECIPIENT=<EMAIL>
ALERT_SMTP_SERVER=smtp.gmail.com
ALERT_SMTP_PORT=587
ALERT_EMAIL_SUBJECT_PREFIX=🚨 GrowthHive Alert
```

### Settings Configuration

The system automatically loads configuration from `app/core/config/settings.py`:

```python
EMAIL_ALERTS_ENABLED: bool = True
ALERT_EMAIL_SENDER: str = "<EMAIL>"
ALERT_EMAIL_PASSWORD: str = "illk owms hody uaqw"
ALERT_EMAIL_RECIPIENT: str = "<EMAIL>"
ALERT_SMTP_SERVER: str = "smtp.gmail.com"
ALERT_SMTP_PORT: int = 587
```

## 🚀 Installation

1. **Install Dependencies**:
   ```bash
   pip install aiosmtplib>=3.0.0
   ```

2. **Update Environment**: Add email configuration to `.env`

3. **Restart Application**: The system will automatically initialize

## 📊 Alert Types

### System Alerts
- **SYSTEM_ERROR**: General system errors
- **DATABASE_ERROR**: Database-related issues
- **API_ERROR**: API endpoint failures
- **AUTHENTICATION_ERROR**: Auth-related problems
- **EXTERNAL_SERVICE_ERROR**: Third-party service failures
- **SECURITY_ALERT**: Security incidents
- **PERFORMANCE_ALERT**: Performance issues
- **CUSTOM**: Custom alerts

### Alert Levels
- **CRITICAL**: Immediate attention required
- **ERROR**: Error conditions
- **WARNING**: Warning conditions
- **INFO**: Informational messages

## 🛠️ Usage Examples

### Basic Error Alert

```python
from app.utils.email_alert import send_error_alert

await send_error_alert(
    title="Payment Processing Failed",
    description="Unable to process payment for order #12345",
    exception=payment_exception,
    additional_data={
        "order_id": "12345",
        "amount": 99.99,
        "payment_method": "credit_card"
    }
)
```

### Database Error Alert

```python
from app.utils.email_alert import send_database_error_alert

await send_database_error_alert(
    operation="INSERT",
    error_message="Connection timeout",
    table_name="users",
    query="INSERT INTO users (email, name) VALUES (?, ?)",
    exception=db_exception
)
```

### Security Alert

```python
from app.services.email_alert_service import email_alert_service

await email_alert_service.send_security_alert(
    event="Suspicious Login",
    description="Multiple failed login attempts",
    user_id="user123",
    ip_address="*************",
    additional_data={
        "failed_attempts": 5,
        "location": "Unknown"
    }
)
```

### Custom Alert

```python
from app.utils.email_alert import send_custom_alert

await send_custom_alert(
    title="High Memory Usage",
    description="Server memory usage exceeded 90%",
    level=AlertLevel.WARNING,
    additional_data={
        "memory_usage": "92%",
        "server": "web-01"
    }
)
```

### Using the Decorator

```python
from app.utils.email_alert import alert_on_error

@alert_on_error(
    alert_title="Payment Processing Error",
    include_args=True,
    alert_type=AlertType.EXTERNAL_SERVICE_ERROR
)
async def process_payment(order_id: str, amount: float):
    # Payment processing logic
    # Automatic alert will be sent if this function raises an exception
    pass
```

## 🔧 API Endpoints

### Test Endpoints

The system includes several test endpoints for verification:

- `GET /api/admin/email-alerts/status` - Get alert system status
- `POST /api/admin/email-alerts/test` - Send test alert
- `POST /api/admin/email-alerts/test-error` - Trigger test error
- `POST /api/admin/email-alerts/custom` - Send custom alert
- `POST /api/admin/email-alerts/test-database-error` - Test database error alert
- `POST /api/admin/email-alerts/test-security-alert` - Test security alert

### Example API Usage

```bash
# Test the alert system
curl -X POST "http://localhost:8000/api/admin/email-alerts/test" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Send custom alert
curl -X POST "http://localhost:8000/api/admin/email-alerts/custom" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Custom Test Alert",
    "description": "This is a custom alert test",
    "alert_type": "CUSTOM",
    "additional_data": {
      "test_key": "test_value"
    }
  }'
```

## 🔒 Security Features

### Rate Limiting
- Prevents alert spam with configurable cooldown periods
- Default: 5 minutes between similar alerts

### Secure Configuration
- Email credentials stored in environment variables
- TLS encryption for SMTP connections
- Input validation for all alert data

### Error Handling
- Graceful degradation if email service is unavailable
- Comprehensive logging for troubleshooting
- Retry logic with exponential backoff

## 📈 Monitoring & Maintenance

### Log Messages
The system logs all alert activities:

```
[INFO] Critical alert sent successfully: Payment Processing Failed
[WARNING] Alert rate limited: SYSTEM_ERROR_Database Connection Failed
[ERROR] Failed to send critical alert: SMTP connection failed
```

### Health Checks
Use the status endpoint to monitor system health:

```python
# Check if alerts are working
response = await client.get("/api/admin/email-alerts/status")
```

### Performance Considerations
- Asynchronous email sending (non-blocking)
- Connection pooling for SMTP
- Rate limiting to prevent overload
- Efficient retry mechanisms

## 🚨 Alert Content Format

Alerts include comprehensive information:

```
🚨 GROWTHHIVE SYSTEM ALERT 🚨
==================================================
Alert Type: SYSTEM_ERROR
Title: Database Connection Failed
Timestamp: 2024-01-01 12:00:00 UTC
Environment: production

DESCRIPTION:
--------------------
Unable to connect to PostgreSQL database

EXCEPTION DETAILS:
--------------------
Type: ConnectionError
Message: Connection timeout after 30 seconds

TRACEBACK:
--------------------
[Full stack trace]

ADDITIONAL DATA:
--------------------
database_host: localhost
database_port: 5432
retry_attempts: 3

==================================================
This is an automated alert from GrowthHive system.
Please investigate and take appropriate action.

System: GrowthHive API v1.0.0
Host: 0.0.0.0:8000
```

## 🔧 Troubleshooting

### Common Issues

1. **Emails not sending**:
   - Check SMTP credentials
   - Verify network connectivity
   - Check Gmail app password settings

2. **Rate limiting too aggressive**:
   - Adjust `_alert_cooldown_minutes` in EmailAlertService

3. **Missing alerts**:
   - Check `EMAIL_ALERTS_ENABLED` setting
   - Verify exception handling integration

### Debug Mode
Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger("app.services.email_alert_service").setLevel(logging.DEBUG)
```

## 📝 Best Practices

1. **Use appropriate alert types** for better categorization
2. **Include relevant context** in additional_data
3. **Don't over-alert** - use rate limiting wisely
4. **Test regularly** using the provided endpoints
5. **Monitor alert volume** to avoid notification fatigue
6. **Keep credentials secure** in environment variables
7. **Use descriptive titles** for quick identification

## 🔄 Integration with Existing Systems

The alert system integrates seamlessly with:

- **Global Exception Handler**: Automatic alerts for unhandled exceptions
- **Security Middleware**: Alerts for security events
- **Database Layer**: Alerts for database errors
- **External Services**: Alerts for API failures
- **Background Tasks**: Alerts for Celery task failures

This robust email alert system ensures you're immediately notified of critical issues in your GrowthHive application, enabling quick response and resolution.

## 🎨 Enhanced Features (v2.0)

### 📧 Rich Email Formatting
- **HTML Email Support**: Beautiful, responsive email design with color-coded severity levels
- **Visual Hierarchy**: Clear sections with emojis and proper spacing for easy scanning
- **Responsive Design**: Emails look great on desktop and mobile devices
- **Syntax Highlighting**: Stack traces with color-coded file paths and line numbers

### 🔍 Comprehensive Context Collection
- **Request Details**: Full HTTP request information (method, path, headers, body, query params)
- **User Context**: User ID, email, role, session information automatically extracted
- **System Metrics**: CPU usage, memory usage, database status, connection pools
- **Performance Data**: Response times, queue lengths, active connections
- **Security Information**: IP addresses, user agents, geolocation data

### 🛡️ Security & Privacy
- **Automatic Data Filtering**: Passwords, tokens, and sensitive headers automatically redacted
- **Request Body Sanitization**: Large payloads truncated, sensitive fields masked
- **IP Geolocation**: Suspicious IP addresses flagged with location information
- **Session Tracking**: Correlate alerts with user sessions for better context

### 🔧 Smart Troubleshooting
- **Context-Aware Hints**: Specific troubleshooting suggestions based on error type
- **Actionable Steps**: Clear next steps for investigation and resolution
- **Related Metrics**: System performance data relevant to the specific error
- **Historical Context**: Information about recent changes and deployments

### 📊 Alert Management
- **Unique Alert IDs**: Each alert has a unique identifier for tracking and correlation
- **Severity Levels**: Visual indicators (🔴 Critical, 🟠 High, 🟡 Medium, 🔵 Info)
- **Rate Limiting**: Intelligent cooldown prevents spam while ensuring critical alerts get through
- **Retry Logic**: Exponential backoff ensures delivery even during network issues

### 🎯 Enhanced Alert Types
Each alert type now includes specialized context:

- **🔴 System Errors**: Stack traces, system metrics, recent deployments
- **🟠 API Errors**: Request details, user context, endpoint performance
- **🚨 Security Alerts**: IP analysis, user behavior patterns, threat indicators
- **🟡 Performance Alerts**: System metrics, affected endpoints, trend analysis
- **🔵 Custom Alerts**: Flexible context based on your specific needs

This enhanced email alert system provides comprehensive visibility into your GrowthHive application's health and performance, enabling rapid issue identification and resolution.
