{"openapi": "3.1.0", "info": {"title": "GrowthHive API", "description": "GrowthHive API for business growth", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["system"], "summary": "Health Check", "description": "Check if the API is running properly", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/metrics": {"get": {"tags": ["system"], "summary": "System Metrics", "description": "Get system performance metrics", "operationId": "metrics_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/": {"get": {"tags": ["system"], "summary": "API Root", "description": "Welcome endpoint with API information", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "example": true}, "message": {"$ref": "#/components/schemas/Message"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"items": {}, "type": "array"}, {"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}], "title": "Data"}, "error_code": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Error Code", "example": 1000}}, "type": "object", "required": ["success", "message"], "title": "ApiResponse"}, "Message": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "default": "", "example": "Data Retrieved Successfully"}, "description": {"type": "string", "title": "Description", "example": "Fetched data successfully."}}, "type": "object", "required": ["description"], "title": "Message"}}}}