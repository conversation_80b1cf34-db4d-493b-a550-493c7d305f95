"""
Robust Email Alert Service for GrowthHive
Handles critical error notifications and system alerts
"""

import asyncio
import json
import sys
import traceback
from datetime import datetime, timezone
from email.message import EmailMessage
from typing import Optional, Dict, Any, List
from enum import Enum

import aiosmtplib
from app.core.config.settings import settings
from app.core.logging import logger


class AlertLevel(str, Enum):
    """Alert severity levels"""
    CRITICAL = "CRITICAL"
    ERROR = "ERROR"
    WARNING = "WARNING"
    INFO = "INFO"


class AlertType(str, Enum):
    """Types of alerts"""
    SYSTEM_ERROR = "SYSTEM_ERROR"
    DATABASE_ERROR = "DATABASE_ERROR"
    API_ERROR = "API_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    SECURITY_ALERT = "SECURITY_ALERT"
    PERFORMANCE_ALERT = "PERFORMANCE_ALERT"
    CUSTOM = "CUSTOM"


class EmailAlertService:
    """
    Robust email alert service for sending critical notifications
    Follows GrowthHive patterns and conventions
    """
    
    def __init__(self):
        self.enabled = settings.EMAIL_ALERTS_ENABLED
        self.sender_email = settings.ALERT_EMAIL_SENDER
        self.sender_password = settings.ALERT_EMAIL_PASSWORD
        self.recipient_email = settings.ALERT_EMAIL_RECIPIENT
        self.smtp_server = settings.ALERT_SMTP_SERVER
        self.smtp_port = settings.ALERT_SMTP_PORT
        self.subject_prefix = settings.ALERT_EMAIL_SUBJECT_PREFIX
        
        # Rate limiting to prevent spam
        self._last_alert_times: Dict[str, datetime] = {}
        self._alert_cooldown_minutes = 5  # Minimum time between similar alerts
        
    async def send_critical_alert(
        self,
        title: str,
        description: str,
        alert_type: AlertType = AlertType.SYSTEM_ERROR,
        additional_data: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None
    ) -> bool:
        """
        Send a critical alert email
        
        Args:
            title: Alert title
            description: Alert description
            alert_type: Type of alert
            additional_data: Additional context data
            exception: Exception object if applicable
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if not self.enabled:
            logger.info("Email alerts are disabled")
            return False
            
        try:
            # Check rate limiting
            alert_key = f"{alert_type}_{title}"
            if self._is_rate_limited(alert_key):
                logger.info(f"Alert rate limited: {alert_key}")
                return False
                
            # Create email content
            subject = f"{self.subject_prefix} - {alert_type.value}: {title}"
            body = self._create_alert_body(
                title=title,
                description=description,
                alert_type=alert_type,
                additional_data=additional_data,
                exception=exception
            )
            
            # Send email
            success = await self._send_email(subject, body)
            
            if success:
                self._update_rate_limit(alert_key)
                logger.info(f"Critical alert sent successfully: {title}")
            else:
                logger.error(f"Failed to send critical alert: {title}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error in send_critical_alert: {e}")
            return False
    
    async def send_exception_alert(
        self,
        exception: Exception,
        request_path: Optional[str] = None,
        request_method: Optional[str] = None,
        user_id: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send an alert for unhandled exceptions
        
        Args:
            exception: The exception that occurred
            request_path: API endpoint path
            request_method: HTTP method
            user_id: User ID if available
            additional_context: Additional context information
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            title = f"Unhandled Exception: {type(exception).__name__}"
            description = str(exception)
            
            context_data = {
                "exception_type": type(exception).__name__,
                "exception_message": str(exception),
                "traceback": traceback.format_exc(),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "environment": settings.ENVIRONMENT
            }
            
            if request_path:
                context_data["request_path"] = request_path
            if request_method:
                context_data["request_method"] = request_method
            if user_id:
                context_data["user_id"] = user_id
            if additional_context:
                context_data.update(additional_context)
                
            return await self.send_critical_alert(
                title=title,
                description=description,
                alert_type=AlertType.SYSTEM_ERROR,
                additional_data=context_data,
                exception=exception
            )
            
        except Exception as e:
            logger.error(f"Error in send_exception_alert: {e}")
            return False
    
    async def send_security_alert(
        self,
        event: str,
        description: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send a security-related alert
        
        Args:
            event: Security event name
            description: Event description
            user_id: User ID if applicable
            ip_address: IP address if applicable
            additional_data: Additional security context
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            context_data = {
                "security_event": event,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "environment": settings.ENVIRONMENT
            }
            
            if user_id:
                context_data["user_id"] = user_id
            if ip_address:
                context_data["ip_address"] = ip_address
            if additional_data:
                context_data.update(additional_data)
                
            return await self.send_critical_alert(
                title=f"Security Alert: {event}",
                description=description,
                alert_type=AlertType.SECURITY_ALERT,
                additional_data=context_data
            )
            
        except Exception as e:
            logger.error(f"Error in send_security_alert: {e}")
            return False
    
    def _is_rate_limited(self, alert_key: str) -> bool:
        """Check if alert is rate limited"""
        if alert_key not in self._last_alert_times:
            return False
            
        last_time = self._last_alert_times[alert_key]
        time_diff = datetime.now(timezone.utc) - last_time
        return time_diff.total_seconds() < (self._alert_cooldown_minutes * 60)
    
    def _update_rate_limit(self, alert_key: str) -> None:
        """Update rate limit timestamp"""
        self._last_alert_times[alert_key] = datetime.now(timezone.utc)
    
    def _create_alert_body(
        self,
        title: str,
        description: str,
        alert_type: AlertType,
        additional_data: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None
    ) -> str:
        """Create user-friendly formatted email body for alerts"""
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")

        # Get severity emoji and color
        severity_info = self._get_severity_info(alert_type)

        body_parts = [
            f"{severity_info['emoji']} GROWTHHIVE ALERT - {severity_info['level']} {severity_info['emoji']}",
            "=" * 70,
            f"🏷️  Alert Type: {alert_type.value}",
            f"📋 Title: {title}",
            f"🕒 Timestamp: {timestamp}",
            f"🌍 Environment: {settings.ENVIRONMENT.upper()}",
            f"⚡ Severity: {severity_info['level']}",
            "",
            "📝 DESCRIPTION:",
            "─" * 40,
            description,
            ""
        ]

        # Add request information if available
        if additional_data:
            request_info = self._extract_request_info(additional_data)
            if request_info:
                body_parts.extend([
                    "🌐 REQUEST INFORMATION:",
                    "─" * 40,
                    *request_info,
                    ""
                ])

        # Add exception details with better formatting
        if exception:
            body_parts.extend([
                "🔥 EXCEPTION DETAILS:",
                "─" * 40,
                f"Exception Type: {type(exception).__name__}",
                f"Exception Message: {str(exception)}",
                ""
            ])

            # Format stack trace for better readability
            stack_trace = traceback.format_exc()
            body_parts.extend([
                "📚 STACK TRACE:",
                "─" * 40,
                self._format_stack_trace(stack_trace),
                ""
            ])

        # Add system context
        system_info = self._get_system_context(additional_data)
        if system_info:
            body_parts.extend([
                "💻 SYSTEM CONTEXT:",
                "─" * 40,
                *system_info,
                ""
            ])

        # Add user context if available
        user_info = self._extract_user_info(additional_data)
        if user_info:
            body_parts.extend([
                "👤 USER CONTEXT:",
                "─" * 40,
                *user_info,
                ""
            ])

        # Add additional data in organized sections
        if additional_data:
            other_data = self._format_additional_data(additional_data)
            if other_data:
                body_parts.extend([
                    "📊 ADDITIONAL DATA:",
                    "─" * 40,
                    *other_data,
                    ""
                ])

        # Add troubleshooting hints
        troubleshooting = self._get_troubleshooting_hints(alert_type, exception)
        if troubleshooting:
            body_parts.extend([
                "🔧 TROUBLESHOOTING HINTS:",
                "─" * 40,
                *troubleshooting,
                ""
            ])

        # Footer with action items
        body_parts.extend([
            "=" * 70,
            "🚨 ACTION REQUIRED:",
            "• Investigate the issue immediately",
            "• Check system logs for more details",
            "• Monitor system performance",
            "• Take corrective action if needed",
            "",
            "📞 SUPPORT INFORMATION:",
            f"• System: {settings.PROJECT_NAME} v{settings.VERSION}",
            f"• Host: {settings.HOST}:{settings.PORT}",
            f"• Environment: {settings.ENVIRONMENT}",
            f"• Alert ID: {self._generate_alert_id()}",
            "",
            "This is an automated alert from GrowthHive monitoring system.",
            "For urgent issues, please contact the development team immediately.",
            "=" * 70
        ])

        return "\n".join(body_parts)

    def _get_severity_info(self, alert_type: AlertType) -> Dict[str, str]:
        """Get severity information for alert type"""
        severity_map = {
            AlertType.SYSTEM_ERROR: {"emoji": "🔴", "level": "CRITICAL"},
            AlertType.DATABASE_ERROR: {"emoji": "🔴", "level": "CRITICAL"},
            AlertType.SECURITY_ALERT: {"emoji": "🚨", "level": "HIGH"},
            AlertType.API_ERROR: {"emoji": "🟠", "level": "HIGH"},
            AlertType.AUTHENTICATION_ERROR: {"emoji": "🟡", "level": "MEDIUM"},
            AlertType.EXTERNAL_SERVICE_ERROR: {"emoji": "🟡", "level": "MEDIUM"},
            AlertType.PERFORMANCE_ALERT: {"emoji": "🟡", "level": "MEDIUM"},
            AlertType.CUSTOM: {"emoji": "🔵", "level": "INFO"}
        }
        return severity_map.get(alert_type, {"emoji": "⚪", "level": "UNKNOWN"})

    def _extract_request_info(self, additional_data: Dict[str, Any]) -> List[str]:
        """Extract and format request information"""
        request_info = []

        # API endpoint information
        if "request_path" in additional_data:
            request_info.append(f"🔗 Endpoint: {additional_data.get('request_method', 'UNKNOWN')} {additional_data['request_path']}")

        # Query parameters
        if "query_params" in additional_data:
            params = additional_data["query_params"]
            if params:
                request_info.append(f"❓ Query Params: {self._format_dict(params)}")

        # Request headers (filtered for security)
        if "headers" in additional_data:
            headers = self._filter_sensitive_headers(additional_data["headers"])
            if headers:
                request_info.append(f"📋 Headers: {self._format_dict(headers)}")

        # Client information
        if "client_host" in additional_data:
            request_info.append(f"🌐 Client IP: {additional_data['client_host']}")

        # Request body (if available and not too large)
        if "request_body" in additional_data:
            body = additional_data["request_body"]
            if body and len(str(body)) < 1000:  # Limit body size
                request_info.append(f"📄 Request Body: {self._format_request_body(body)}")

        return request_info

    def _extract_user_info(self, additional_data: Dict[str, Any]) -> List[str]:
        """Extract and format user information"""
        user_info = []

        if "user_id" in additional_data:
            user_info.append(f"🆔 User ID: {additional_data['user_id']}")

        if "user_email" in additional_data:
            user_info.append(f"📧 User Email: {additional_data['user_email']}")

        if "user_role" in additional_data:
            user_info.append(f"👤 User Role: {additional_data['user_role']}")

        if "session_id" in additional_data:
            user_info.append(f"🔑 Session ID: {additional_data['session_id']}")

        if "user_agent" in additional_data:
            user_info.append(f"🌐 User Agent: {additional_data['user_agent']}")

        return user_info

    def _get_system_context(self, additional_data: Dict[str, Any]) -> List[str]:
        """Get system context information"""
        system_info = [
            f"🖥️  Server: {settings.HOST}:{settings.PORT}",
            f"🐍 Python Version: {sys.version.split()[0]}",
            f"⚡ Environment: {settings.ENVIRONMENT}",
            f"🕒 Server Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ]

        # Add database info if available
        if "database_error" in additional_data:
            system_info.append(f"🗄️  Database: PostgreSQL (Error occurred)")

        # Add memory/performance info if available
        if "memory_usage" in additional_data:
            system_info.append(f"💾 Memory Usage: {additional_data['memory_usage']}")

        if "cpu_usage" in additional_data:
            system_info.append(f"⚙️  CPU Usage: {additional_data['cpu_usage']}")

        return system_info

    def _format_stack_trace(self, stack_trace: str) -> str:
        """Format stack trace for better readability"""
        lines = stack_trace.split('\n')
        formatted_lines = []

        for line in lines:
            if line.strip():
                if line.startswith('  File'):
                    formatted_lines.append(f"📁 {line.strip()}")
                elif line.startswith('    '):
                    formatted_lines.append(f"   ➤ {line.strip()}")
                else:
                    formatted_lines.append(f"   {line.strip()}")

        return '\n'.join(formatted_lines)

    def _format_additional_data(self, additional_data: Dict[str, Any]) -> List[str]:
        """Format additional data, excluding already processed fields"""
        excluded_fields = {
            'request_path', 'request_method', 'query_params', 'headers', 'client_host',
            'request_body', 'user_id', 'user_email', 'user_role', 'session_id', 'user_agent',
            'database_error', 'memory_usage', 'cpu_usage', 'traceback'
        }

        other_data = []
        for key, value in additional_data.items():
            if key not in excluded_fields:
                formatted_value = self._format_value(value)
                other_data.append(f"• {key}: {formatted_value}")

        return other_data

    def _get_troubleshooting_hints(self, alert_type: AlertType, exception: Optional[Exception]) -> List[str]:
        """Get troubleshooting hints based on alert type and exception"""
        hints = []

        if alert_type == AlertType.DATABASE_ERROR:
            hints.extend([
                "• Check database connection and credentials",
                "• Verify database server is running",
                "• Check for connection pool exhaustion",
                "• Review recent database migrations"
            ])
        elif alert_type == AlertType.API_ERROR:
            hints.extend([
                "• Check API endpoint implementation",
                "• Verify request parameters and validation",
                "• Review authentication and authorization",
                "• Check for rate limiting issues"
            ])
        elif alert_type == AlertType.SECURITY_ALERT:
            hints.extend([
                "• Review security logs immediately",
                "• Check for suspicious IP addresses",
                "• Verify user authentication patterns",
                "• Consider temporary access restrictions"
            ])
        elif alert_type == AlertType.PERFORMANCE_ALERT:
            hints.extend([
                "• Monitor system resources (CPU, Memory, Disk)",
                "• Check for memory leaks or resource exhaustion",
                "• Review recent deployments or changes",
                "• Consider scaling resources if needed"
            ])

        # Add exception-specific hints
        if exception:
            exception_type = type(exception).__name__
            if "Connection" in exception_type:
                hints.append("• Check network connectivity and firewall settings")
            elif "Timeout" in exception_type:
                hints.append("• Increase timeout values or optimize slow operations")
            elif "Permission" in exception_type or "Auth" in exception_type:
                hints.append("• Verify user permissions and authentication tokens")

        return hints

    def _filter_sensitive_headers(self, headers: Dict[str, Any]) -> Dict[str, Any]:
        """Filter out sensitive headers for security"""
        sensitive_headers = {'authorization', 'cookie', 'x-api-key', 'x-auth-token'}
        filtered = {}

        for key, value in headers.items():
            if key.lower() not in sensitive_headers:
                filtered[key] = value
            else:
                filtered[key] = "[REDACTED]"

        return filtered

    def _format_dict(self, data: Dict[str, Any]) -> str:
        """Format dictionary for display"""
        if not data:
            return "None"

        items = []
        for key, value in data.items():
            items.append(f"{key}={self._format_value(value)}")

        return ", ".join(items)

    def _format_value(self, value: Any) -> str:
        """Format a value for display"""
        if isinstance(value, str) and len(value) > 100:
            return f"{value[:97]}..."
        elif isinstance(value, (dict, list)) and len(str(value)) > 200:
            return f"{str(value)[:197]}..."
        else:
            return str(value)

    def _format_request_body(self, body: Any) -> str:
        """Format request body for display"""
        if isinstance(body, dict):
            # Filter sensitive fields
            filtered_body = {}
            sensitive_fields = {'password', 'token', 'secret', 'key', 'auth'}

            for key, value in body.items():
                if any(sensitive in key.lower() for sensitive in sensitive_fields):
                    filtered_body[key] = "[REDACTED]"
                else:
                    filtered_body[key] = value

            return json.dumps(filtered_body, indent=2)
        else:
            return str(body)

    def _generate_alert_id(self) -> str:
        """Generate a unique alert ID for tracking"""
        import uuid
        return str(uuid.uuid4())[:8].upper()

    def _create_html_body(
        self,
        title: str,
        description: str,
        alert_type: AlertType,
        additional_data: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None
    ) -> str:
        """Create HTML formatted email body for better readability"""
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        severity_info = self._get_severity_info(alert_type)
        alert_id = self._generate_alert_id()

        # Color scheme based on severity
        color_map = {
            "CRITICAL": "#dc3545",
            "HIGH": "#fd7e14",
            "MEDIUM": "#ffc107",
            "INFO": "#0dcaf0",
            "UNKNOWN": "#6c757d"
        }
        primary_color = color_map.get(severity_info['level'], "#6c757d")

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>GrowthHive Alert - {title}</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }}
                .container {{ max-width: 800px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, {primary_color}, {primary_color}dd); color: white; padding: 20px; border-radius: 8px 8px 0 0; }}
                .header h1 {{ margin: 0; font-size: 24px; }}
                .severity {{ display: inline-block; background: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }}
                .content {{ padding: 20px; }}
                .section {{ margin-bottom: 25px; }}
                .section-title {{ color: {primary_color}; font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 2px solid {primary_color}; padding-bottom: 5px; }}
                .info-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }}
                .info-item {{ background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 4px solid {primary_color}; }}
                .info-label {{ font-weight: bold; color: #495057; font-size: 12px; text-transform: uppercase; }}
                .info-value {{ color: #212529; margin-top: 4px; }}
                .code-block {{ background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; overflow-x: auto; }}
                .stack-trace {{ background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 11px; overflow-x: auto; }}
                .troubleshooting {{ background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 6px; padding: 15px; }}
                .troubleshooting ul {{ margin: 0; padding-left: 20px; }}
                .footer {{ background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border-top: 1px solid #dee2e6; }}
                .alert-id {{ font-family: 'Courier New', monospace; background: #e9ecef; padding: 2px 6px; border-radius: 4px; }}
                .emoji {{ font-size: 18px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1><span class="emoji">{severity_info['emoji']}</span> GrowthHive System Alert</h1>
                    <div class="severity">{severity_info['level']} PRIORITY</div>
                </div>

                <div class="content">
                    <div class="section">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Alert Type</div>
                                <div class="info-value">{alert_type.value}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Timestamp</div>
                                <div class="info-value">{timestamp}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Environment</div>
                                <div class="info-value">{settings.ENVIRONMENT.upper()}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Alert ID</div>
                                <div class="info-value"><span class="alert-id">{alert_id}</span></div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">📋 Description</div>
                        <div class="code-block">{description}</div>
                    </div>
        """

        # Add request information if available
        if additional_data:
            request_info = self._extract_request_info(additional_data)
            if request_info:
                html_content += f"""
                    <div class="section">
                        <div class="section-title">🌐 Request Information</div>
                        <div class="info-grid">
                """

                for info in request_info:
                    if ":" in info:
                        label, value = info.split(":", 1)
                        label = label.strip().replace("🔗", "").replace("❓", "").replace("📋", "").replace("🌐", "").replace("📄", "")
                        html_content += f"""
                            <div class="info-item">
                                <div class="info-label">{label}</div>
                                <div class="info-value">{value.strip()}</div>
                            </div>
                        """

                html_content += "</div></div>"

        # Add exception details
        if exception:
            stack_trace = self._format_stack_trace_html(traceback.format_exc())
            html_content += f"""
                <div class="section">
                    <div class="section-title">🔥 Exception Details</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Exception Type</div>
                            <div class="info-value">{type(exception).__name__}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Exception Message</div>
                            <div class="info-value">{str(exception)}</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <div class="info-label" style="margin-bottom: 8px;">Stack Trace:</div>
                        <div class="stack-trace">{stack_trace}</div>
                    </div>
                </div>
            """

        # Add troubleshooting hints
        troubleshooting = self._get_troubleshooting_hints(alert_type, exception)
        if troubleshooting:
            html_content += f"""
                <div class="section">
                    <div class="section-title">🔧 Troubleshooting Hints</div>
                    <div class="troubleshooting">
                        <ul>
                            {''.join(f'<li>{hint.replace("• ", "")}</li>' for hint in troubleshooting)}
                        </ul>
                    </div>
                </div>
            """

        # Footer
        html_content += f"""
                </div>

                <div class="footer">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <div class="info-label">System Information</div>
                            <div>• {settings.PROJECT_NAME} v{settings.VERSION}</div>
                            <div>• Host: {settings.HOST}:{settings.PORT}</div>
                            <div>• Environment: {settings.ENVIRONMENT}</div>
                        </div>
                        <div>
                            <div class="info-label">Action Required</div>
                            <div>• Investigate immediately</div>
                            <div>• Check system logs</div>
                            <div>• Take corrective action</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6; text-align: center; color: #6c757d; font-size: 12px;">
                        This is an automated alert from GrowthHive monitoring system.
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

        return html_content

    def _format_stack_trace_html(self, stack_trace: str) -> str:
        """Format stack trace for HTML display"""
        lines = stack_trace.split('\n')
        formatted_lines = []

        for line in lines:
            if line.strip():
                if line.startswith('  File'):
                    formatted_lines.append(f'<span style="color: #81c784;">📁 {line.strip()}</span>')
                elif line.startswith('    '):
                    formatted_lines.append(f'<span style="color: #ffb74d;">   ➤ {line.strip()}</span>')
                else:
                    formatted_lines.append(f'<span style="color: #f48fb1;">   {line.strip()}</span>')

        return '<br>'.join(formatted_lines)

    async def _send_email(self, subject: str, body: str) -> bool:
        """
        Send email using aiosmtplib with both HTML and text versions

        Args:
            subject: Email subject
            body: Email body (text version)

        Returns:
            bool: True if sent successfully, False otherwise
        """
        try:
            # Create multipart email message
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText

            message = MIMEMultipart('alternative')
            message["From"] = self.sender_email
            message["To"] = self.recipient_email
            message["Subject"] = subject

            # Add text version
            text_part = MIMEText(body, 'plain', 'utf-8')
            message.attach(text_part)

            # Create and add HTML version (extract info from text body for HTML generation)
            # For now, we'll use the text version, but this can be enhanced
            html_body = body.replace('\n', '<br>').replace('  ', '&nbsp;&nbsp;')
            html_part = MIMEText(f"<html><body><pre style='font-family: monospace; white-space: pre-wrap;'>{html_body}</pre></body></html>", 'html', 'utf-8')
            message.attach(html_part)

            # Send email with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # Use STARTTLS for Gmail
                    await aiosmtplib.send(
                        message,
                        hostname=self.smtp_server,
                        port=self.smtp_port,
                        username=self.sender_email,
                        password=self.sender_password,
                        start_tls=True,  # Use STARTTLS instead of use_tls
                        timeout=30
                    )
                    logger.info(f"Alert email sent successfully on attempt {attempt + 1}")
                    return True

                except Exception as e:
                    logger.warning(f"Email send attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    else:
                        logger.error(f"All email send attempts failed: {e}")
                        return False

        except Exception as e:
            logger.error(f"Error creating/sending email: {e}")
            return False


# Global email alert service instance
email_alert_service = EmailAlertService()
