#!/usr/bin/env python3
"""
Direct analysis of the Coochie Hydrogreen content and optimal parameter testing
"""

import asyncio
import sys
import json
import psycopg
from typing import Dict, List, Any, Optional

# Add the project root to the path
sys.path.append('..')

async def get_actual_content():
    """Get the actual text content from the franchisor embedding"""
    try:
        from docqa.config import get_config
        
        config = get_config()
        db_url = config.database_url.replace("postgresql+asyncpg://", "postgresql://")
        
        # Connect to database
        conn = psycopg.connect(db_url)
        cur = conn.cursor()
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Get franchisor data
        cur.execute("""
            SELECT 
                id,
                name,
                region,
                brochure_url,
                embedding
            FROM franchisors 
            WHERE id = %s
        """, (franchisor_id,))
        
        result = cur.fetchone()
        
        if result:
            print(f"✅ Found franchisor: {result[1]}")
            print(f"   Region: {result[2]}")
            print(f"   Brochure URL: {result[3]}")
            
            # The embedding might contain the actual text content
            embedding_data = result[4]
            print(f"   Embedding type: {type(embedding_data)}")
            print(f"   Embedding length: {len(embedding_data) if embedding_data else 0}")
            
            # Try to extract text if it's stored as text
            if isinstance(embedding_data, str):
                print(f"   Content preview: {embedding_data[:500]}...")
                return embedding_data
            elif hasattr(embedding_data, '__iter__'):
                print(f"   Embedding is a vector with {len(embedding_data)} dimensions")
                return None
            else:
                print(f"   Unknown embedding format")
                return None
        else:
            print("❌ No franchisor found")
            return None
            
    except Exception as e:
        print(f"💥 Error getting content: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()


async def test_with_different_thresholds():
    """Test question answering with different similarity thresholds"""
    try:
        from docqa.central_api import ask_question
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Test questions
        test_questions = [
            "What training does the business provide?",
            "What are the franchisee fees?",
            "What is FRANCHISEE APPROVAL PROCESS?",
            "What are the specialised Coochie Hydrogreen franchise?",
            "What is Coochie Hydrogreen?"
        ]
        
        # Different threshold configurations
        threshold_configs = [
            {"threshold": 0.5, "top_k": 5, "name": "High Threshold (0.5)"},
            {"threshold": 0.3, "top_k": 10, "name": "Medium Threshold (0.3)"},
            {"threshold": 0.1, "top_k": 15, "name": "Low Threshold (0.1)"},
            {"threshold": 0.05, "top_k": 20, "name": "Very Low Threshold (0.05)"},
            {"threshold": 0.01, "top_k": 25, "name": "Ultra Low Threshold (0.01)"},
            {"threshold": 0.001, "top_k": 30, "name": "Minimal Threshold (0.001)"}
        ]
        
        print("\n🧪 Testing different similarity thresholds:")
        print("=" * 80)
        
        results = {}
        
        for config in threshold_configs:
            print(f"\n🔬 Testing: {config['name']}")
            print(f"   Threshold: {config['threshold']}, Top-K: {config['top_k']}")
            
            config_results = []
            
            for question in test_questions:
                print(f"   Q: {question[:40]}...")
                
                request = {
                    "question": question,
                    "franchisor_id": franchisor_id,
                    "similarity_threshold": config["threshold"],
                    "top_k": config["top_k"],
                    "temperature": 0.7,
                    "max_tokens": 600,
                    "force_refresh": True
                }
                
                try:
                    result = await ask_question(request)
                    
                    if result.get('success'):
                        answer = result.get('answer', '')
                        sources = result.get('sources', [])
                        
                        # Check if we got actual content
                        has_content = not any(phrase in answer.lower() for phrase in [
                            'does not include', 'does not contain', 'unable to provide',
                            'not available', 'cannot provide', 'no information',
                            "couldn't find relevant information"
                        ])
                        
                        config_results.append({
                            'question': question,
                            'answer': answer,
                            'sources_count': len(sources),
                            'has_content': has_content,
                            'answer_length': len(answer)
                        })
                        
                        status = "✅" if has_content else "❌"
                        print(f"      {status} Sources: {len(sources)}, Length: {len(answer)}, Content: {has_content}")
                        
                        if has_content:
                            print(f"      Answer: {answer[:100]}...")
                    else:
                        print(f"      💥 Error: {result.get('error', 'Unknown')}")
                        
                except Exception as e:
                    print(f"      💥 Exception: {str(e)}")
            
            # Calculate success rate
            successful_answers = sum(1 for r in config_results if r.get('has_content', False))
            success_rate = successful_answers / len(config_results) if config_results else 0
            
            print(f"   📊 Success Rate: {success_rate:.1%} ({successful_answers}/{len(config_results)})")
            
            results[config['name']] = {
                'config': config,
                'results': config_results,
                'success_rate': success_rate,
                'successful_answers': successful_answers
            }
        
        return results
        
    except Exception as e:
        print(f"💥 Error testing thresholds: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


async def analyze_vector_search_directly():
    """Directly analyze vector search results"""
    try:
        from docqa.vector_store import PgVectorStore, EmbeddingService
        
        vector_store = PgVectorStore()
        embedding_service = EmbeddingService()
        
        # Test with a simple question
        question = "What is Coochie Hydrogreen?"
        question_embedding = embedding_service.generate_embedding(question)
        
        print(f"\n🔍 Direct vector search analysis for: '{question}'")
        print("=" * 60)
        
        # Test different similarity thresholds
        thresholds = [0.9, 0.7, 0.5, 0.3, 0.1, 0.05, 0.01, 0.001, 0.0]
        
        for threshold in thresholds:
            print(f"\nThreshold: {threshold}")
            
            try:
                search_results = vector_store.search_similar(
                    query_embedding=question_embedding,
                    top_k=10,
                    similarity_threshold=threshold,
                    table_priority=['franchisors', 'documents']
                )
                
                print(f"  Results found: {len(search_results)}")
                
                for i, result in enumerate(search_results[:3]):  # Show top 3
                    similarity = getattr(result, 'similarity_score', 'N/A')
                    table = getattr(result, 'table_name', 'Unknown')
                    content = getattr(result, 'content', getattr(result, 'text', ''))[:100]
                    
                    print(f"    {i+1}. Table: {table}, Similarity: {similarity}, Content: {content}...")
                    
            except Exception as e:
                print(f"  Error: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"💥 Error in direct analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def find_optimal_parameters():
    """Find the optimal parameters for question answering"""
    try:
        print("\n🎯 Finding optimal parameters...")
        
        # Test content extraction
        content = await get_actual_content()
        
        # Test vector search directly
        await analyze_vector_search_directly()
        
        # Test different thresholds
        threshold_results = await test_with_different_thresholds()
        
        # Analyze results
        print("\n📊 PARAMETER ANALYSIS RESULTS:")
        print("=" * 50)
        
        if threshold_results:
            # Sort by success rate
            sorted_results = sorted(
                threshold_results.items(),
                key=lambda x: x[1]['success_rate'],
                reverse=True
            )
            
            print("\nRanked by success rate:")
            for i, (name, data) in enumerate(sorted_results, 1):
                config = data['config']
                print(f"{i}. {name}")
                print(f"   Success Rate: {data['success_rate']:.1%}")
                print(f"   Threshold: {config['threshold']}, Top-K: {config['top_k']}")
                print(f"   Successful Answers: {data['successful_answers']}")
                print()
            
            # Find best configuration
            if sorted_results:
                best_config = sorted_results[0][1]['config']
                print(f"🏆 OPTIMAL PARAMETERS:")
                print(f"   Similarity Threshold: {best_config['threshold']}")
                print(f"   Top-K: {best_config['top_k']}")
                print(f"   Success Rate: {sorted_results[0][1]['success_rate']:.1%}")
        
        # Save results
        analysis_data = {
            'content_found': content is not None,
            'content_preview': content[:500] if content else None,
            'threshold_results': threshold_results
        }
        
        with open('optimal_parameters_analysis.json', 'w') as f:
            json.dump(analysis_data, f, indent=2, default=str)
        
        print("\n✅ Analysis saved to optimal_parameters_analysis.json")
        
        return analysis_data
        
    except Exception as e:
        print(f"💥 Error finding optimal parameters: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main analysis function"""
    print("🚀 Starting optimal parameter analysis for Coochie Hydrogreen...")
    
    results = await find_optimal_parameters()
    
    print("\n🎉 Analysis completed!")
    return results


if __name__ == "__main__":
    asyncio.run(main())
