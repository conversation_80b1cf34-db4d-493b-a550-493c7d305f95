#!/usr/bin/env python3
"""
Demo script showing how to use industry filtering in franchisor API
"""

import asyncio
import httpx
import json

BASE_URL = "http://localhost:8000"

async def demo_industry_filtering():
    """Demonstrate industry filtering with both ID and name"""
    
    print("🔍 Industry Filtering Demo for Franchisor API")
    print("=" * 50)
    
    # Use existing user credentials (from our previous test)
    async with httpx.AsyncClient() as client:
        # Register a new user for demo
        import uuid
        user_email = f"demo_{str(uuid.uuid4())[:8]}@example.com"
        register_data = {
            "email": user_email,
            "password": "Demo123!",
            "confirm_password": "Demo123!",
            "first_name": "Demo",
            "last_name": "User",
            "mobile": f"+{str(uuid.uuid4().int)[:10]}"
        }

        response = await client.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code != 201:
            print("❌ Registration failed")
            return

        # Login with new user
        login_data = {
            "email_or_mobile": user_email,
            "password": "Demo123!"
        }
        
        response = await client.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ Login failed. Please ensure you have a valid user account.")
            return
        
        data = response.json()
        access_token = data["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        print("✅ Logged in successfully\n")
        
        # Get available industries
        print("📋 Available Industries:")
        response = await client.get(f"{BASE_URL}/api/industries", headers=headers)
        if response.status_code == 200:
            industries_data = response.json()
            industries = industries_data.get("data", {}).get("items", [])
            
            for i, industry in enumerate(industries[:3], 1):  # Show first 3
                print(f"   {i}. {industry['name']} (ID: {industry['id']})")
            
            if industries:
                test_industry = industries[0]
                industry_id = test_industry["id"]
                industry_name = test_industry["name"]
                
                print(f"\n🎯 Testing with: {industry_name}")
                print("-" * 30)
                
                # Method 1: Filter by Industry ID (UUID)
                print("Method 1: Filter by Industry ID (UUID)")
                response = await client.get(
                    f"{BASE_URL}/api/franchisors/",
                    params={"industry": industry_id, "limit": 3},
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    items = data.get("data", {}).get("items", [])
                    print(f"✅ Found {len(items)} franchisors using ID filter")
                    for item in items:
                        print(f"   - {item['name']} (Industry: {item.get('industry_details', {}).get('name', 'N/A')})")
                else:
                    print(f"❌ ID filter failed: {response.status_code}")
                
                print()
                
                # Method 2: Filter by Industry Name
                print("Method 2: Filter by Industry Name")
                response = await client.get(
                    f"{BASE_URL}/api/franchisors/",
                    params={"industry": industry_name, "limit": 3},
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    items = data.get("data", {}).get("items", [])
                    print(f"✅ Found {len(items)} franchisors using name filter")
                    for item in items:
                        print(f"   - {item['name']} (Industry: {item.get('industry_details', {}).get('name', 'N/A')})")
                else:
                    print(f"❌ Name filter failed: {response.status_code}")
                
                print()
                
                # Method 3: Partial Name Match
                partial_name = industry_name[:4] if len(industry_name) > 4 else industry_name
                print(f"Method 3: Partial Name Match ('{partial_name}')")
                response = await client.get(
                    f"{BASE_URL}/api/franchisors/",
                    params={"industry": partial_name, "limit": 3},
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    items = data.get("data", {}).get("items", [])
                    print(f"✅ Found {len(items)} franchisors using partial name")
                    for item in items:
                        print(f"   - {item['name']} (Industry: {item.get('industry_details', {}).get('name', 'N/A')})")
                else:
                    print(f"❌ Partial name filter failed: {response.status_code}")
                
                print("\n" + "=" * 50)
                print("📝 Summary:")
                print("   ✅ You can filter by Industry ID (UUID)")
                print("   ✅ You can filter by Industry Name (exact or partial)")
                print("   ✅ Invalid UUIDs are treated as name searches")
                print("   ✅ Name searches are case-insensitive")
                
        else:
            print(f"❌ Failed to get industries: {response.status_code}")

if __name__ == "__main__":
    asyncio.run(demo_industry_filtering())
