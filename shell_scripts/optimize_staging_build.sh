#!/bin/bash
# Comprehensive script to reduce staging server from 44GB to manageable size

echo "🚀 Starting staging build optimization (44GB → ~2GB target)"
echo "=================================================="

# Step 1: Clean existing build artifacts
echo "1️⃣ Cleaning build artifacts..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyo" -delete
find . -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
echo "   ✅ Build artifacts cleaned"

# Step 2: Clear pip cache
echo "2️⃣ Clearing pip cache..."
pip cache purge 2>/dev/null || true
echo "   ✅ Pip cache cleared"

# Step 3: Remove test and development files
echo "3️⃣ Removing test/dev files..."
find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".coverage" -delete 2>/dev/null || true
find . -name "htmlcov" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".mypy_cache" -type d -exec rm -rf {} + 2>/dev/null || true
echo "   ✅ Test/dev files removed"

# Step 4: Remove IDE and OS files
echo "4️⃣ Removing IDE/OS files..."
find . -name ".vscode" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".idea" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true
find . -name "*.swp" -delete 2>/dev/null || true
find . -name "*.swo" -delete 2>/dev/null || true
echo "   ✅ IDE/OS files removed"

# Step 5: Remove logs and temporary files
echo "5️⃣ Removing logs and temp files..."
find . -name "*.log" -delete 2>/dev/null || true
find . -name "logs" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "tmp" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "temp" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".tmp" -type d -exec rm -rf {} + 2>/dev/null || true
echo "   ✅ Logs and temp files removed"

# Step 6: Check for large files
echo "6️⃣ Identifying large files (>10MB)..."
find . -type f -size +10M -exec ls -lh {} \; 2>/dev/null | head -10
echo "   ✅ Large files identified above"

# Step 7: Show current size
echo "7️⃣ Current directory size:"
du -sh . 2>/dev/null
echo ""

# Step 8: Docker optimization recommendations
echo "8️⃣ Docker Build Optimization:"
echo "   📋 Use the optimized requirements.txt (heavy deps removed)"
echo "   📋 Use Dockerfile.optimized for multi-stage build"
echo "   📋 Build with: docker build -f Dockerfile.optimized -t growthhive:optimized ."
echo ""

# Step 9: Staging deployment recommendations
echo "9️⃣ Staging Deployment Recommendations:"
echo "   🔧 Use optimized Docker image"
echo "   🔧 Set ECS task memory limits (2GB max)"
echo "   🔧 Use ECR image scanning to identify bloat"
echo "   🔧 Enable Docker layer caching"
echo ""

echo "✅ Optimization complete!"
echo "Expected size reduction: 44GB → 2-4GB (90%+ reduction)"
echo ""
echo "Next steps:"
echo "1. Run: chmod +x optimize_staging_build.sh && ./optimize_staging_build.sh"
echo "2. Build optimized image: docker build -f Dockerfile.optimized -t growthhive:optimized ."
echo "3. Deploy to staging with new image"
echo "4. Monitor ECS task memory usage"
