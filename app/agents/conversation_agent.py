"""
Conversational SMS Chatbot Agent
Handles structured conversation flow for lead engagement, qualification, and Q&A
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import structlog
from langchain_core.messages import AIMessage
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry
from app.core.database.connection import get_db
from contextlib import asynccontextmanager
from app.models.sales_script import SalesScript
from app.models.pre_qualification_question import PreQualificationQuestion
from app.models.conversation_session import ConversationSession
from app.models.lead import Lead, LeadResponse
from app.services.holiday_availability_service import HolidayAvailabilityService
from app.services.escalation_search_service import EscalationSearchService

logger = structlog.get_logger()


@asynccontextmanager
async def get_db_session():
    """Helper to get database session as context manager"""
    async for db in get_db():
        yield db
        break


class ConversationStage:
    """Constants for conversation stages"""
    INITIAL_GREETING = "initial_greeting"
    PREQUALIFICATION = "prequalification"
    DOCUMENT_QA = "document_qa"
    FOLLOWUP = "followup"
    GOODBYE = "goodbye"


class ConversationAgent(BaseAgent):
    """
    Conversational SMS chatbot agent that manages structured conversation flow
    """
    
    def _initialize_tools(self):
        """Initialize conversation-specific tools"""
        tool_names = ["search_documents", "create_lead", "create_communication", "store_memory"]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process conversation state through structured flow"""
        try:
            user_input = state.get("user_input", "")
            context = state.get("context", {})
            phone_number = context.get("sender", "")

            if not phone_number:
                state["error"] = "Phone number not found in context"
                return state

            # Check admin availability first
            async with get_db_session() as db:
                availability_service = HolidayAvailabilityService(db)
                is_available, out_of_reach_message = await availability_service.check_admin_availability()

                if not is_available and out_of_reach_message:
                    print(f"🏖️ Admin unavailable due to holiday - terminating conversation")

                    # Mark any existing session as completed due to unavailability
                    session = await self._get_or_create_session(phone_number, user_input)
                    session.is_completed = True
                    session.completion_reason = "admin_unavailable"
                    session.completed_at = datetime.now(timezone.utc)
                    await self._update_session(session, user_input, out_of_reach_message)

                    # Return the out of reach message and terminate
                    state["response"] = out_of_reach_message
                    state["session_data"] = {
                        "session_id": session.session_id,
                        "stage": "terminated",
                        "completion_reason": "admin_unavailable"
                    }
                    return state

            # Get or create conversation session
            session = await self._get_or_create_session(phone_number, user_input)
            
            # Process based on current stage
            print(f"🔄 Processing stage: {session.current_stage} for phone: {phone_number}")
            if session.current_stage == ConversationStage.INITIAL_GREETING:
                print("📞 Handling initial greeting")
                response = await self._handle_initial_greeting(session, user_input)
            elif session.current_stage == ConversationStage.PREQUALIFICATION:
                response = await self._handle_prequalification(session, user_input)
            elif session.current_stage == ConversationStage.DOCUMENT_QA:
                response = await self._handle_document_qa(session, user_input)
            elif session.current_stage == ConversationStage.FOLLOWUP:
                response = await self._handle_followup(session, user_input)
            elif session.current_stage == ConversationStage.GOODBYE:
                response = await self._handle_goodbye(session, user_input)
            else:
                response = "I'm sorry, there seems to be an issue with our conversation. Let me restart."
                await self._reset_session(session)
            
            # Update session with response
            await self._update_session(session, user_input, response)

            # Get updated session from database to ensure we have the latest state
            async with get_db_session() as db:
                stmt = select(ConversationSession).where(
                    ConversationSession.phone_number == phone_number
                )
                result = await db.execute(stmt)
                updated_session = result.scalar_one_or_none()
                if updated_session:
                    session = updated_session

            # Update state
            state["response"] = response
            state["next_action"] = "end"
            state["messages"] = state.get("messages", []) + [AIMessage(content=response)]
            state["session_data"] = {
                "session_id": session.session_id,
                "stage": session.current_stage,
                "phone_number": phone_number
            }
            
            logger.info(f"Conversation processed - Stage: {session.current_stage}, Phone: {phone_number}")
            return state
            
        except Exception as e:
            logger.error(f"Error in conversation agent: {str(e)}")
            state["error"] = str(e)
            state["response"] = "I apologize, but I encountered an error. Please try again."
            return state
    
    async def _get_or_create_session(self, phone_number: str, user_input: str) -> ConversationSession:
        """Get existing session or create new one"""
        async with get_db_session() as db:
            session_id = f"sms_{phone_number}"
            
            # Try to get existing active session
            stmt = select(ConversationSession).where(
                and_(
                    ConversationSession.phone_number == phone_number,
                    ConversationSession.is_active == True,
                    ConversationSession.is_completed == False
                )
            )
            result = await db.execute(stmt)
            session = result.scalar_one_or_none()
            
            if not session:
                # Create new session
                session = ConversationSession(
                    phone_number=phone_number,
                    session_id=session_id,
                    current_stage=ConversationStage.INITIAL_GREETING,
                    last_message_received=user_input,
                    message_count=1
                )
                db.add(session)
                await db.commit()
                await db.refresh(session)
                logger.info(f"Created new conversation session for {phone_number}")
            
            return session
    
    async def _handle_initial_greeting(self, session: ConversationSession, user_input: str) -> str:
        """Handle initial greeting stage"""
        print(f"📞 Initial greeting - Current stage: {session.current_stage}")

        # Get greeting script
        async with get_db_session() as db:
            stmt = select(SalesScript).where(
                and_(
                    SalesScript.script_title == "Initial greeting",
                    SalesScript.is_active == True,
                    SalesScript.is_deleted == False
                )
            )
            result = await db.execute(stmt)
            script = result.scalar_one_or_none()

        # Update the session object directly (will be committed in _update_session)
        print(f"📞 Moving to prequalification stage")
        session.current_stage = ConversationStage.PREQUALIFICATION
        print(f"📞 Stage updated to: {session.current_stage}")

        if script:
            return script.script_content
        else:
            return "Hi! I'm here to help you explore the franchise opportunity. Let's get started!"
    
    async def _handle_prequalification(self, session: ConversationSession, user_input: str) -> str:
        """Handle prequalification stage"""
        print(f"❓ Prequalification - questions_asked: {session.questions_asked}, current_question_id: {session.current_question_id}")
        # If this is the first message in prequalification, send introduction
        if session.questions_asked == 0:
            # Get qualification introduction script
            async with get_db_session() as db:
                stmt = select(SalesScript).where(
                    and_(
                        SalesScript.script_title == "Qualification Introduction",
                        SalesScript.is_active == True,
                        SalesScript.is_deleted == False
                    )
                )
                result = await db.execute(stmt)
                script = result.scalar_one_or_none()

                intro_message = script.script_content if script else "I'd like to ask you a few questions to better understand your interests."

                # Get first question
                first_question = await self._get_next_question(db, session)
                if first_question:
                    # Update session object directly (will be committed in _update_session)
                    session.current_question_id = first_question.id
                    session.questions_asked = 1
                    return f"{intro_message}\n\n{first_question.question_text}"
                else:
                    # No questions available, move to document Q&A
                    session.current_stage = ConversationStage.DOCUMENT_QA
                    return await self._handle_document_qa_introduction(db, session)
            
        # Process answer to current question
        if session.current_question_id:
            async with get_db_session() as db:
                await self._process_qualification_answer(db, session, user_input)

                # Check if more questions
                next_question = await self._get_next_question(db, session)
                if next_question:
                    # Update session object directly (will be committed in _update_session)
                    session.current_question_id = next_question.id
                    session.questions_asked += 1
                    return next_question.question_text
                else:
                    # All questions answered, evaluate qualification
                    qualification_result = await self._evaluate_qualification(db, session)

                    # Move to document Q&A stage
                    session.current_stage = ConversationStage.DOCUMENT_QA

                    return f"{qualification_result}\n\n{await self._handle_document_qa_introduction(db, session)}"

        return "I didn't understand your response. Could you please try again?"

    async def _handle_document_qa(self, session: ConversationSession, user_input: str) -> str:
        """Handle document Q&A stage"""
        # Check if user wants to end conversation
        if user_input.lower().strip() in ["no", "no thanks", "that's all", "goodbye", "bye", "done", "finished", "end", "stop", "quit"]:
            session.current_stage = ConversationStage.GOODBYE
            return await self._handle_goodbye(session, user_input)

        # First check if the question is franchise-related
        if not self._is_franchise_related_question(user_input):
            print(f"🔍 NON-FRANCHISE QUESTION DETECTED: {user_input[:50]}...")
            logger.info(f"Question detected as non-franchise-related: {user_input[:50]}...")
            # Before giving non-franchise response, check for similar resolved questions
            print("🔍 SEARCHING FOR SIMILAR RESOLVED QUESTIONS...")
            logger.info("Searching for similar resolved questions...")
            similar_resolved = await self._search_similar_resolved_questions(user_input)
            if similar_resolved:
                print("✅ FOUND SIMILAR RESOLVED QUESTION!")
                logger.info("Found similar resolved question, returning resolved answer")
                return similar_resolved

            print("❌ NO SIMILAR RESOLVED QUESTIONS FOUND")
            logger.info("No similar resolved questions found, generating non-franchise response")

            # Generate non-franchise response and assess its quality
            non_franchise_response = self._get_non_franchise_response(user_input)

            # If the non-franchise response is also inadequate, escalate
            if self._is_inadequate_ai_response(user_input, non_franchise_response):
                logger.info(f"Non-franchise response also inadequate, escalating: {user_input[:50]}...")
                phone_number = self._get_phone_from_session()
                if phone_number:
                    escalated = await self._escalate_franchise_question(user_input, phone_number)
                    if escalated:
                        logger.info(f"Successfully escalated non-franchise question to escalation_question_bank")

                return "I don't have specific information about that topic. Your question has been noted and our team will follow up with you. In the meantime, is there anything about our franchise opportunity I can help with? (Type 'done' when finished)"

            return non_franchise_response

        # Use RAG system to answer franchise questions
        try:
            from docqa.production_integration import ProductionRAGSystem
            rag_system = ProductionRAGSystem()

            # Answer the question using RAG
            result = await rag_system.answer_question(
                question=user_input,
                similarity_threshold=0.1,  # Low threshold for better recall
                top_k=6,
                temperature=0.3
            )

            if result.get('success') and result.get('answer'):
                answer = result['answer']

                # Comprehensive analysis: Check if the AI response adequately answers the question
                if self._is_inadequate_ai_response(user_input, answer):
                    logger.info(f"RAG response inadequate for question '{user_input[:50]}...': {answer[:100]}...")
                    # Check for similar resolved questions first
                    similar_resolved = await self._search_similar_resolved_questions(user_input)
                    if similar_resolved:
                        return similar_resolved

                    # Escalate to escalation_question_bank
                    phone_number = self._get_phone_from_session()
                    if phone_number:
                        escalated = await self._escalate_franchise_question(user_input, phone_number)
                        if escalated:
                            logger.info(f"Successfully escalated inadequate franchise answer to escalation_question_bank")

                    return "I don't have specific information about that in our current materials. Your question has been noted and our team will follow up with detailed information. In the meantime, is there anything else about our franchise opportunity I can help with? (Type 'done' when finished)"

                # Add follow-up prompt for successful answers
                return f"{answer}\n\nDo you have any other questions about our franchise opportunity? (Type 'done' when finished)"
            else:
                # RAG completely failed, check for similar resolved questions before escalating
                logger.info(f"RAG failed to answer franchise question: {user_input[:50]}...")
                similar_resolved = await self._search_similar_resolved_questions(user_input)
                if similar_resolved:
                    return similar_resolved

                # Escalate to escalation_question_bank
                phone_number = self._get_phone_from_session()
                if phone_number:
                    await self._escalate_franchise_question(user_input, phone_number)

                return "I don't have specific information about that in our current materials. Your question has been noted and our team will follow up with detailed information. In the meantime, is there anything else about our franchise opportunity I can help with? (Type 'done' when finished)"

        except Exception as e:
            logger.error(f"Error in document Q&A: {str(e)}")
            return "I'm sorry, I'm having trouble accessing our franchise information right now. Could you try asking your question again? (Type 'done' when finished)"

    def _is_franchise_related_question(self, question: str) -> bool:
        """Check if a question is related to franchise business"""
        question_lower = question.lower()

        # Franchise-related keywords
        franchise_keywords = [
            "franchise", "franchising", "franchisor", "franchisee",
            "investment", "invest", "fee", "fees", "cost", "costs", "price", "pricing",
            "royalty", "royalties", "revenue", "profit", "roi", "return",
            "training", "support", "territory", "location", "area",
            "business opportunity", "requirements", "qualification",
            "application", "process", "timeline", "start", "launch",
            "marketing", "advertising", "brand", "system", "operations",
            "coochie", "hydrogreen", "hydro green", "business model",
            "ongoing fees", "initial investment", "franchise agreement",
            "territory rights", "franchisor support", "franchise training"
        ]

        # Check if question contains franchise keywords
        return any(keyword in question_lower for keyword in franchise_keywords)

    def _get_non_franchise_response(self, question: str) -> str:
        """Generate appropriate response for non-franchise questions"""
        question_lower = question.lower()

        # Specific responses for common non-franchise topics
        if any(word in question_lower for word in ["weather", "temperature", "rain", "sunny", "cloudy"]):
            return "I don't have access to weather information. I'm here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

        elif any(word in question_lower for word in ["sports", "football", "basketball", "soccer", "game", "score"]):
            return "I don't have access to sports information. I'm here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

        elif any(word in question_lower for word in ["news", "politics", "election", "government"]):
            return "I don't have access to news or political information. I'm here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

        elif any(word in question_lower for word in ["time", "date", "today", "tomorrow", "yesterday"]):
            return "I don't have access to current time or date information. I'm here to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

        else:
            # Generic response for other non-franchise questions
            return "I don't have information about that topic. I'm specifically designed to help with questions about our Coochie HydroGreen franchise opportunity. What would you like to know about our franchise? (Type 'done' when finished)"

    async def _search_similar_resolved_questions(self, question: str) -> Optional[str]:
        """Search for similar resolved questions in escalation_question_bank for the same franchisor"""
        try:
            logger.info(f"Starting search for similar resolved questions: {question[:50]}...")

            # Get franchisor_id for the current lead
            phone_number = self._get_phone_from_session()
            if not phone_number:
                logger.warning("No phone number available for franchisor lookup")
                return None

            franchisor_id = await self._get_franchisor_id_for_lead(phone_number)
            logger.info(f"Searching for resolved questions for franchisor: {franchisor_id}")

            async with get_db_session() as db:
                escalation_service = EscalationSearchService(db)

                # Search for similar resolved questions within the SAME franchisor only
                similar_questions = await escalation_service.search_similar_resolved_questions(
                    question=question,
                    franchisor_id=franchisor_id,
                    similarity_threshold=0.85,  # 85% similarity threshold to prevent wrong matches
                    limit=1  # Return best match only
                )

                logger.info(f"Search completed, found {len(similar_questions) if similar_questions else 0} results")

                if similar_questions:
                    best_match = similar_questions[0]
                    logger.info(f"Found similar resolved question: {best_match['question'][:50]}... (similarity: {best_match['similarity_score']:.2f})")

                    # Store the resolved answer info in the session for webhook to detect
                    self._mark_as_resolved_answer(question, best_match)

                    # Also mark that this should be added to question_bank
                    self._mark_for_question_bank_addition(question, best_match)

                    return escalation_service.format_resolved_answer(best_match)

                logger.info("No similar resolved questions found")
                return None

        except Exception as e:
            logger.error(f"Error searching similar resolved questions: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    async def _escalate_franchise_question(self, question: str, phone_number: str) -> bool:
        """Escalate a franchise-related question to escalation_question_bank when AI cannot answer"""
        try:
            logger.info(f"Escalating franchise question to escalation_question_bank: {question[:50]}...")

            async with get_db_session() as db:
                # Get lead information
                from app.services.lead_service import LeadService
                lead_service = LeadService(db)
                lead = await lead_service.get_lead_by_phone(phone_number)

                if not lead:
                    logger.warning(f"No lead found for phone {phone_number}, cannot escalate question")
                    return False

                # Get the lead's franchisor_id to ensure proper binding
                franchisor_id = str(lead.franchisor_id) if lead.franchisor_id else "569976f2-d845-4615-8a91-96e18086adbe"
                logger.info(f"Escalating question for franchisor: {franchisor_id}")

                # Use the question classification tool to add to escalation_question_bank
                from app.agents.tools.question_bank_tools import insert_escalation_question_bank

                result = await insert_escalation_question_bank(
                    db=db,
                    question_text=question,
                    lead_id=str(lead.id),
                    franchisor_id=franchisor_id,
                    reason="AI could not provide adequate answer for franchise-related question"
                )

                logger.info(f"Successfully escalated franchise question: {question[:50]}...")
                return True

        except Exception as e:
            logger.error(f"Failed to escalate franchise question: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    def _is_inadequate_ai_response(self, question: str, response: str) -> bool:
        """Comprehensive analysis to determine if the AI response adequately answers the question"""
        if not response or len(response.strip()) < 20:
            return True

        response_lower = response.lower()
        question_lower = question.lower()

        # 1. Check for explicit failure phrases
        failure_phrases = [
            "i don't know",
            "i don't have information",
            "i cannot provide",
            "i'm not sure",
            "i don't have access",
            "i couldn't find",
            "i'm sorry, i don't",
            "i don't have specific information",
            "i'm unable to",
            "i don't have that information",
            "i'm not able to",
            "i don't have details",
            "i cannot answer",
            "i'm not certain",
            "i don't have enough information",
            "are not explicitly outlined",
            "specific details are not available",
            "we currently do not have specific information"
        ]

        if any(phrase in response_lower for phrase in failure_phrases):
            return True

        # 2. Check if response is just redirecting to contact without providing substance
        contact_redirect_phrases = [
            "please let me know the best way to reach you",
            "contact us directly at",
            "email us at",
            "call us at",
            "i'd be happy to get you more specific information"
        ]

        has_contact_redirect = any(phrase in response_lower for phrase in contact_redirect_phrases)

        # If it's mostly just contact redirection, check if there's substantial content
        if has_contact_redirect:
            # Remove common filler content to check substance
            substantial_content = response_lower
            for phrase in contact_redirect_phrases:
                substantial_content = substantial_content.replace(phrase, "")

            # Remove common filler words and phrases
            filler_phrases = [
                "hello!", "thank you", "feel free to ask", "we're here to help",
                "do you have any other questions", "type 'done' when finished",
                "about our franchise opportunity", "coochie hydrogreen"
            ]

            for filler in filler_phrases:
                substantial_content = substantial_content.replace(filler, "")

            # If less than 100 characters of substantial content remain, it's inadequate
            if len(substantial_content.strip()) < 100:
                return True

        # 3. Check if response addresses the specific question asked
        return self._does_answer_address_question(question_lower, response_lower)

    def _does_answer_address_question(self, question_lower: str, response_lower: str) -> bool:
        """Check if the answer specifically addresses the question asked"""

        # Extract key question words and topics
        question_keywords = self._extract_question_keywords(question_lower)

        # Check if the response addresses the key topics
        addressed_keywords = 0
        for keyword in question_keywords:
            if keyword in response_lower:
                addressed_keywords += 1

        # If less than 30% of key topics are addressed, it's inadequate
        if len(question_keywords) > 0:
            coverage_ratio = addressed_keywords / len(question_keywords)
            if coverage_ratio < 0.3:
                return True  # Inadequate

        # Check for specific question types that need specific answers
        if any(word in question_lower for word in ['how much', 'cost', 'price', 'fee']):
            # Cost questions should have numbers or specific pricing info
            if not any(char.isdigit() or word in response_lower
                      for word in ['$', 'dollar', 'aud', 'percent', '%', 'thousand', 'million']
                      for char in response_lower):
                return True  # Inadequate - no pricing information

        if any(word in question_lower for word in ['when', 'how long', 'timeline', 'time']):
            # Time questions should have temporal information
            time_words = ['day', 'week', 'month', 'year', 'hour', 'minute', 'immediately', 'soon', 'typically']
            if not any(word in response_lower for word in time_words):
                return True  # Inadequate - no time information

        if any(word in question_lower for word in ['where', 'location', 'address']):
            # Location questions should have place information
            location_words = ['address', 'location', 'city', 'state', 'country', 'street', 'area', 'region']
            if not any(word in response_lower for word in location_words):
                return True  # Inadequate - no location information

        return False  # Answer appears adequate

    def _extract_question_keywords(self, question_lower: str) -> list:
        """Extract key keywords from the question that should be addressed in the answer"""
        # Remove common question words
        stop_words = ['what', 'how', 'when', 'where', 'why', 'who', 'which', 'is', 'are', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']

        words = question_lower.split()
        keywords = []

        for word in words:
            # Clean word
            clean_word = word.strip('.,!?()[]{}":;')

            # Skip stop words and short words
            if clean_word not in stop_words and len(clean_word) > 2:
                keywords.append(clean_word)

        return keywords

    def _get_phone_from_session(self) -> Optional[str]:
        """Extract phone number from current session context"""
        try:
            # This will be set by the webhook when processing
            if hasattr(self, '_current_phone_number'):
                return self._current_phone_number
            return None
        except Exception as e:
            logger.error(f"Error getting phone from session: {str(e)}")
            return None

    async def _get_franchisor_id_for_lead(self, phone_number: str) -> Optional[str]:
        """Get franchisor_id for a lead based on their phone number"""
        try:
            async with get_db_session() as db:
                from app.services.lead_service import LeadService
                lead_service = LeadService(db)
                lead = await lead_service.get_lead_by_phone(phone_number)

                if lead and lead.franchisor_id:
                    return str(lead.franchisor_id)

                # Default to Coochie Hydrogreen if no specific franchisor found
                return "569976f2-d845-4615-8a91-96e18086adbe"

        except Exception as e:
            logger.error(f"Error getting franchisor_id for lead: {str(e)}")
            # Default to Coochie Hydrogreen
            return "569976f2-d845-4615-8a91-96e18086adbe"

    def _mark_as_resolved_answer(self, question: str, resolved_match: Dict[str, Any]):
        """Mark that this response came from a resolved escalation question"""
        # Store in class attribute that webhook can check
        if not hasattr(self, '_resolved_answer_info'):
            self._resolved_answer_info = {}

        self._resolved_answer_info[question] = {
            'found_resolved_answer': True,
            'original_question': resolved_match['question'],
            'similarity_score': resolved_match['similarity_score'],
            'resolved_question_id': resolved_match['id']
        }

    def _mark_for_question_bank_addition(self, question: str, resolved_match: Dict[str, Any]):
        """Mark that this question should also be added to question_bank"""
        if not hasattr(self, '_add_to_question_bank'):
            self._add_to_question_bank = {}

        # Extract answer text from resolved match
        answer = resolved_match.get('answer', '')
        if isinstance(answer, dict):
            if 'text' in answer:
                answer_text = answer['text']
            elif 'content' in answer:
                answer_text = answer['content']
            else:
                answer_text = str(answer)
        elif isinstance(answer, list):
            answer_text = ', '.join(str(item) for item in answer)
        else:
            answer_text = str(answer)

        self._add_to_question_bank[question] = {
            'should_add_to_question_bank': True,
            'original_question': resolved_match['question'],
            'answer': answer_text,
            'similarity_score': resolved_match['similarity_score'],
            'resolved_question_id': resolved_match['id']
        }

    async def _handle_followup(self, session: ConversationSession, user_input: str) -> str:
        """Handle followup stage"""
        # Check if user wants to end conversation
        if user_input.lower().strip() in ["no", "no thanks", "that's all", "goodbye", "bye", "done", "finished", "end", "stop", "quit"]:
            session.current_stage = ConversationStage.GOODBYE
            return await self._handle_goodbye(session, user_input)
        else:
            # Treat any other input as a question and handle in document Q&A
            session.current_stage = ConversationStage.DOCUMENT_QA
            return await self._handle_document_qa(session, user_input)

    async def _handle_goodbye(self, session: ConversationSession, user_input: str) -> str:
        """Handle goodbye stage"""
        async with get_db_session() as db:
            # Get goodbye script
            stmt = select(SalesScript).where(
                and_(
                    SalesScript.script_title == "Goodbye",
                    SalesScript.is_active == True,
                    SalesScript.is_deleted == False
                )
            )
            result = await db.execute(stmt)
            script = result.scalar_one_or_none()

            # Mark session as completed
            session.is_completed = True
            session.completion_reason = "goodbye"
            session.completed_at = datetime.now(timezone.utc)
            await db.commit()

            if script:
                return script.script_content
            else:
                return "Thank you for your interest! Have a great day and feel free to reach out if you have any more questions."

    async def _handle_document_qa_introduction(self, db: AsyncSession, session: ConversationSession) -> str:
        """Get document Q&A introduction message"""
        stmt = select(SalesScript).where(
            and_(
                SalesScript.script_title == "Document Q&A Introduction",
                SalesScript.is_active == True,
                SalesScript.is_deleted == False
            )
        )
        result = await db.execute(stmt)
        script = result.scalar_one_or_none()

        if script:
            return script.script_content
        else:
            return "Now I can answer questions about our franchise opportunity based on our brochure. What would you like to know?"

    async def _get_next_question(self, db: AsyncSession, session: ConversationSession) -> Optional[PreQualificationQuestion]:
        """Get the next pre-qualification question"""
        stmt = select(PreQualificationQuestion).where(
            and_(
                PreQualificationQuestion.is_active == True,
                PreQualificationQuestion.is_deleted == False,
                PreQualificationQuestion.order_sequence > session.questions_asked
            )
        ).order_by(PreQualificationQuestion.order_sequence).limit(1)

        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def _process_qualification_answer(self, db: AsyncSession, session: ConversationSession, answer: str):
        """Process and store qualification answer"""
        if not session.current_question_id or not session.lead_id:
            # Create lead if it doesn't exist
            if not session.lead_id:
                # Default lead status ID for "New Lead"
                import uuid
                default_lead_status_id = uuid.UUID("f53c50cf-9374-4d18-98c1-c905215051eb")

                lead = Lead(
                    first_name=f"SMS Lead",
                    last_name=session.phone_number,
                    phone=session.phone_number,
                    lead_status_id=default_lead_status_id
                )
                db.add(lead)
                await db.commit()
                await db.refresh(lead)

                session.lead_id = lead.id
                await db.commit()

        # Store the response
        if session.lead_id and session.current_question_id:
            lead_response = LeadResponse(
                lead_id=session.lead_id,
                question_id=session.current_question_id,
                response_text=answer
            )
            db.add(lead_response)
            session.questions_answered += 1
            await db.commit()

    async def _evaluate_qualification(self, db: AsyncSession, session: ConversationSession) -> str:
        """Evaluate lead qualification based on answers"""
        if not session.lead_id:
            return "Thank you for your responses."

        # Simple qualification logic - can be enhanced
        qualification_threshold = 0.8  # 80%

        # Get all questions and responses
        stmt = select(LeadResponse).where(LeadResponse.lead_id == session.lead_id)
        result = await db.execute(stmt)
        responses = result.scalars().all()

        if len(responses) > 0:
            # Simple scoring: assume all responses are positive for now
            # In real implementation, you'd analyze the actual responses
            score = min(len(responses) / 3.0, 1.0)  # Assume 3 questions for full qualification
            session.qualification_score = f"{score * 100:.1f}%"
            session.is_qualified = score >= qualification_threshold

            # Update lead status
            stmt = select(Lead).where(Lead.id == session.lead_id)
            result = await db.execute(stmt)
            lead = result.scalar_one_or_none()

            if lead:
                lead.qualification_status = "qualified" if session.is_qualified else "unqualified"
                await db.commit()

            if session.is_qualified:
                return f"Great! Based on your responses, you seem like a good fit for this franchise opportunity (Score: {session.qualification_score})."
            else:
                return f"Thank you for your responses. While this particular opportunity might not be the perfect fit (Score: {session.qualification_score}), I can still answer questions about it."

        return "Thank you for your responses."

    async def _update_session(self, session: ConversationSession, user_input: str, bot_response: str):
        """Update session with latest message exchange"""
        async with get_db_session() as db:
            # Merge the session object into the current database session
            merged_session = await db.merge(session)
            merged_session.last_message_received = user_input
            merged_session.last_message_sent = bot_response
            merged_session.message_count += 1
            merged_session.last_activity_at = datetime.now(timezone.utc)
            # Ensure all session fields are updated
            merged_session.current_stage = session.current_stage
            merged_session.questions_asked = session.questions_asked
            merged_session.current_question_id = session.current_question_id
            await db.commit()
            await db.refresh(merged_session)
            # Update the original session object with the committed values
            session.current_stage = merged_session.current_stage
            session.last_message_received = merged_session.last_message_received
            session.last_message_sent = merged_session.last_message_sent
            session.message_count = merged_session.message_count
            session.last_activity_at = merged_session.last_activity_at
            session.questions_asked = merged_session.questions_asked
            session.current_question_id = merged_session.current_question_id

    async def _reset_session(self, session: ConversationSession):
        """Reset session to initial state"""
        async with get_db_session() as db:
            session.current_stage = ConversationStage.INITIAL_GREETING
            session.current_question_id = None
            session.questions_asked = 0
            session.questions_answered = 0
            session.qualification_score = None
            session.is_qualified = None
            await db.commit()

    def get_conversation_capabilities(self) -> List[str]:
        """Get list of conversation capabilities"""
        return [
            "Initial greeting and welcome",
            "Pre-qualification questioning",
            "Document-based Q&A using RAG",
            "Lead scoring and qualification",
            "Conversation state management",
            "Graceful conversation termination"
        ]
