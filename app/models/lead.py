"""
Lead model for database operations
"""
import uuid
from sqlalchemy import Column, String, DateTime, Text, Numeric, Integer, Float, ForeignKey, Boolean, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from app.core.database.connection import Base


class Lead(Base):
    __tablename__ = "leads"

    # Core identification fields
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    zoho_lead_id = Column(String(100), unique=True)

    # Name fields (split from full_name)
    first_name = Column(String(100), nullable=False, index=True)
    last_name = Column(String(100), nullable=True, index=True)

    # Contact information
    phone = Column(String(20), index=True)
    mobile = Column(String(20), index=True)
    email = Column(String(255), index=True)
    location = Column(String(255))
    postal_code = Column(String(20), index=True)

    # Reference fields (now using foreign keys)
    lead_source_id = Column(UUID(as_uuid=True), ForeignKey("lead_sources.id"), nullable=True, index=True)
    lead_status_id = Column(UUID(as_uuid=True), ForeignKey("lead_statuses.id"), nullable=False, index=True)
    brand_preference = Column(UUID(as_uuid=True), ForeignKey("franchisors.id"), nullable=True)
    budget_preference = Column(String(100))

    # Franchise buyer information fields
    franchise_interested_in = Column(Text)
    looking_for_business_opportunity_since = Column(String(100))
    skills = Column(Text)
    looking_to_be_owner_operator = Column(String(50))
    when_looking_to_start = Column(String(100))
    ethnic_background = Column(String(100))
    funds_to_invest = Column(String(100))
    eoi_nda_link = Column(Text)
    work_background = Column(String(100))
    motivation_to_enquire = Column(String(100))
    funds_available = Column(String(100))
    motivation = Column(Text)
    have_run_business_before = Column(Boolean)
    have_mortgage = Column(Boolean)
    high_net_worth = Column(String(100))

    # Status fields
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    responses = relationship("LeadResponse", back_populates="lead")
    communications = relationship("Communication", back_populates="lead")
    conversation_messages = relationship("ConversationMessage", back_populates="lead")
    franchisor = relationship("Franchisor", foreign_keys=[brand_preference], lazy="select")
    lead_source_rel = relationship("LeadSource", foreign_keys=[lead_source_id], lazy="select")
    lead_status_rel = relationship("LeadStatus", foreign_keys=[lead_status_id], lazy="select")

    def __repr__(self):
        return f"<Lead(id={self.id}, name={self.first_name} {self.last_name}, status={self.lead_status_id})>"


# Note: Question model removed - use PreQualificationQuestion from app.models.pre_qualification_question instead


class LeadResponse(Base):
    __tablename__ = "lead_responses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"))
    question_id = Column(UUID(as_uuid=True), ForeignKey("pre_qualification_questions.id"))
    response_text = Column(Text)
    answered_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    lead = relationship("Lead", back_populates="responses")
    # Note: question relationship removed to simplify questions module update

    def __repr__(self):
        return f"<LeadResponse(id={self.id}, lead_id={self.lead_id}, question_id={self.question_id})>"


class Communication(Base):
    __tablename__ = "communications"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False)
    communication_type = Column(String(50), nullable=False)  # email, phone, note, meeting, etc.
    subject = Column(String(255))
    content = Column(Text)
    direction = Column(String(20))  # inbound, outbound, internal
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    lead = relationship("Lead", back_populates="communications")

    def __repr__(self):
        return f"<Communication(id={self.id}, lead_id={self.lead_id}, type={self.communication_type})>"
