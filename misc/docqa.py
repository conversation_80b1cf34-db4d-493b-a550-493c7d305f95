#!/usr/bin/env python3
"""
Enhanced DocQA CLI Entry Point with Background Processing

This script provides an enhanced entry point for the DocQA system with:
- Background processing with immediate response
- Parallel document processing
- Smart chunking and section detection
- Chart analysis and OCR
- Bulk vector operations

Usage:
    # Background processing (immediate response)
    python docqa.py ingest s3://bucket/document.pdf --background

    # Synchronous processing
    python docqa.py ingest s3://bucket/document.pdf --sync

    # Ask questions (legacy system)
    python docqa.py ask "What is the refund policy?"

    # Ask questions (production-grade system)
    python docqa.py ask-production "What is <PERSON>och<PERSON> Hydrogreen?"

    # Check task status
    python docqa.py status <task_id>

    # Get processing statistics
    python docqa.py stats
"""

import sys
import time
from pathlib import Path
from typing import Optional
import typer
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import the enhanced central processor
from docqa.central_processor import CentralDocumentProcessor
from docqa.cache.document_cache import DocumentCache

app = typer.Typer(help="Enhanced DocQA System with Background Processing")
console = Console()

# Global processor instance
processor: Optional[CentralDocumentProcessor] = None
cache: Optional[DocumentCache] = None

def get_processor() -> CentralDocumentProcessor:
    """Get or create the global processor instance"""
    global processor
    if processor is None:
        processor = CentralDocumentProcessor()
    return processor

def get_cache() -> DocumentCache:
    """Get or create the global cache instance"""
    global cache
    if cache is None:
        cache = DocumentCache()
    return cache

@app.command()
def ingest(
    source: str = typer.Argument(..., help="Document source (URL, S3 path, or local file)"),
    table: str = typer.Option("documents", "--table", "-t", help="Target table (documents or franchisors)"),
    background: bool = typer.Option(True, "--background/--sync", help="Process in background (default) or synchronously"),
    force: bool = typer.Option(False, "--force", "-f", help="Force reprocessing even if cached"),
    charts: bool = typer.Option(True, "--charts/--no-charts", help="Extract and analyze charts"),
    tables: bool = typer.Option(True, "--tables/--no-tables", help="Extract tables"),
    ocr: bool = typer.Option(True, "--ocr/--no-ocr", help="Use OCR for images"),
    chunk_size: int = typer.Option(400, "--chunk-size", help="Target tokens per chunk"),
    chunk_overlap: int = typer.Option(50, "--chunk-overlap", help="Overlap tokens between chunks"),
    wait: bool = typer.Option(False, "--wait", "-w", help="Wait for background task completion")
):
    """Ingest a document with enhanced processing"""

    console.print("[bold blue]Enhanced DocQA Document Ingestion[/bold blue]")
    console.print(f"Source: {source}")
    console.print(f"Table: {table}")
    console.print(f"Mode: {'Background' if background else 'Synchronous'}")
    console.print()

    try:
        proc = get_processor()

        if background:
            # Background processing
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Submitting document for processing...", total=None)

                task_id = proc.process_document(
                    source=source,
                    target_table=table,
                    background=True,
                    force_processing=force,
                    extract_charts=charts,
                    extract_tables=tables,
                    use_ocr=ocr,
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap
                )

                progress.update(task, description="Task submitted successfully!")

            console.print("[green]✓[/green] Task submitted successfully!")
            console.print(f"[bold]Task ID:[/bold] {task_id}")
            console.print(f"[dim]Use 'python docqa.py status {task_id}' to check progress[/dim]")

            if wait:
                console.print("\n[yellow]Waiting for task completion...[/yellow]")
                result = proc.wait_for_task(task_id, timeout=300)  # 5 minute timeout

                if result:
                    if result.success:
                        console.print("[green]✓[/green] Processing completed!")
                        console.print(f"Chunks created: {result.chunks_created}")
                        console.print(f"Processing time: {result.processing_time:.2f}s")
                    else:
                        console.print(f"[red]✗[/red] Processing failed: {result.error_message}")
                else:
                    console.print("[yellow]⚠[/yellow] Task timeout or not found")

        else:
            # Synchronous processing
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Processing document...", total=None)

                result = proc.process_document(
                    source=source,
                    target_table=table,
                    background=False,
                    force_processing=force,
                    extract_charts=charts,
                    extract_tables=tables,
                    use_ocr=ocr,
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap
                )

                progress.update(task, description="Processing completed!")

            if result.success:
                console.print("[green]✓[/green] Processing completed successfully!")
                console.print(f"Document ID: {result.document_id}")
                console.print(f"Chunks created: {result.chunks_created}")
                console.print(f"Processing time: {result.processing_time:.2f}s")

                if result.metadata:
                    console.print(f"Sections detected: {result.metadata.get('sections_detected', 0)}")
                    console.print(f"Charts detected: {result.metadata.get('charts_detected', 0)}")
                    console.print(f"Tables extracted: {result.metadata.get('tables_extracted', 0)}")
            else:
                console.print(f"[red]✗[/red] Processing failed: {result.error_message}")
                raise typer.Exit(1)

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)

@app.command()
def ask(
    question: str = typer.Argument(..., help="Question to ask"),
    top_k: int = typer.Option(6, "--top-k", "-k", help="Number of top results to retrieve"),
    threshold: float = typer.Option(0.7, "--threshold", "-t", help="Similarity threshold"),
    context: bool = typer.Option(True, "--context/--no-context", help="Include context information")
):
    """Ask a question with enhanced context (legacy system)"""

    console.print(f"[bold blue]Question:[/bold blue] {question}")
    console.print()

    try:
        proc = get_processor()

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Processing question...", total=None)

            response = proc.ask_question(
                question=question,
                top_k=top_k,
                similarity_threshold=threshold,
                include_context=context
            )

            progress.update(task, description="Question processed!")

        # Display answer
        if hasattr(response, 'answer'):
            console.print(Panel(response.answer, title="Answer", border_style="green"))
        elif isinstance(response, dict) and 'answer' in response:
            console.print(Panel(response['answer'], title="Answer", border_style="green"))
        else:
            console.print(Panel(str(response), title="Response", border_style="blue"))

        # Display sources if available
        sources = getattr(response, 'sources', response.get('sources', []) if isinstance(response, dict) else [])
        if sources and context:
            console.print("\n[bold]Sources:[/bold]")
            for i, source in enumerate(sources[:3], 1):  # Show top 3 sources
                source_info = f"Source {i}"
                if hasattr(source, 'metadata') and source.metadata:
                    doc_id = source.metadata.get('document_id', 'Unknown')
                    source_info += f" (Doc: {doc_id[:8]}...)"

                content = getattr(source, 'content', str(source))[:200] + "..."
                console.print(f"[dim]{source_info}:[/dim] {content}")

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)

@app.command("ask-production")
def ask_production(
    question: str = typer.Argument(..., help="Question to ask"),
    franchisor_id: str = typer.Option(None, "--franchisor-id", "-f", help="Optional franchisor ID filter"),
    top_k: int = typer.Option(5, "--top-k", "-k", help="Number of top results to retrieve"),
    threshold: float = typer.Option(0.5, "--threshold", "-t", help="Similarity threshold"),
    temperature: float = typer.Option(0.1, "--temperature", help="Generation temperature (0.0-1.0)"),
    context: bool = typer.Option(True, "--context/--no-context", help="Include context information"),
    json_output: bool = typer.Option(False, "--json", help="Output in JSON format")
):
    """Ask a question using production-grade RAG system"""

    console.print(f"[bold green]Production RAG Question:[/bold green] {question}")
    if franchisor_id:
        console.print(f"[dim]Franchisor Filter: {franchisor_id}[/dim]")
    console.print()

    try:
        import asyncio
        from docqa.serve import ask_question_production

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Processing with production RAG...", total=None)

            # Run the async function
            response = asyncio.run(ask_question_production(
                question=question,
                franchisor_id=franchisor_id,
                top_k=top_k,
                similarity_threshold=threshold,
                temperature=temperature,
                include_metadata=context,
                format='json' if json_output else 'text'
            ))

            progress.update(task, description="Question processed!")

        if json_output:
            console.print(response)
        else:
            # Parse the response if it's JSON
            try:
                import json
                parsed_response = json.loads(response)

                # Display answer
                answer = parsed_response.get('answer', response)
                console.print(Panel(answer, title="Production RAG Answer", border_style="green"))

                # Display metadata
                if context and 'metadata' in parsed_response:
                    metadata = parsed_response['metadata']
                    console.print(f"\n[dim]Processing Time: {metadata.get('processing_time', 0):.2f}s[/dim]")
                    console.print(f"[dim]Model: {metadata.get('model_used', 'Unknown')}[/dim]")
                    console.print(f"[dim]Chunks Found: {metadata.get('chunks_found', 0)}[/dim]")
                    console.print(f"[dim]Similarity Threshold: {metadata.get('similarity_threshold_used', 0):.2f}[/dim]")

                # Display sources
                if context and 'sources' in parsed_response:
                    sources = parsed_response['sources']
                    if sources:
                        console.print("\n[bold]Sources:[/bold]")
                        for i, source in enumerate(sources[:3], 1):
                            score = source.get('similarity_score', 0)
                            text_preview = source.get('text', '')[:150] + "..."
                            console.print(f"[dim]Source {i} (Score: {score:.3f}):[/dim] {text_preview}")

            except json.JSONDecodeError:
                # If not JSON, display as plain text
                console.print(Panel(response, title="Production RAG Answer", border_style="green"))

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)


@app.command("ask-brochure")
def ask_brochure(
    question: str = typer.Argument(..., help="Question about company brochure"),
    franchisor_id: str = typer.Option(None, "--franchisor-id", "-f", help="Optional franchisor ID filter"),
    top_k: int = typer.Option(5, "--top-k", "-k", help="Number of top results to retrieve"),
    threshold: float = typer.Option(0.4, "--threshold", "-t", help="Similarity threshold (lower for brochures)"),
    temperature: float = typer.Option(0.2, "--temperature", help="Generation temperature (0.0-1.0)"),
    context: bool = typer.Option(True, "--context/--no-context", help="Include context information"),
    json_output: bool = typer.Option(False, "--json", help="Output in JSON format")
):
    """Ask a question using brochure-optimized RAG system"""
    
    console.print(f"[bold cyan]Brochure Question:[/bold cyan] {question}")
    if franchisor_id:
        console.print(f"[dim]Franchisor Filter: {franchisor_id}[/dim]")
    console.print()

    try:
        import asyncio
        from docqa.serve import ask_brochure_question

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Processing with brochure RAG...", total=None)

            # Run the async function
            response = asyncio.run(ask_brochure_question(
                question=question,
                franchisor_id=franchisor_id,
                top_k=top_k,
                similarity_threshold=threshold,
                temperature=temperature,
                include_metadata=context,
                format='json' if json_output else 'text'
            ))

            progress.update(task, description="Brochure question processed!")

        if json_output:
            console.print(response)
        else:
            # Parse the response if it's JSON
            try:
                import json
                parsed_response = json.loads(response)
                
                # Display answer
                answer = parsed_response.get('answer', response)
                console.print(Panel(answer, title="Brochure Answer", border_style="cyan"))
                
                # Display metadata
                if context and 'metadata' in parsed_response:
                    metadata = parsed_response['metadata']
                    console.print(f"\n[dim]Processing Time: {metadata.get('processing_time', 0):.2f}s[/dim]")
                    console.print(f"[dim]Model: {metadata.get('model_used', 'Unknown')}[/dim]")
                    console.print(f"[dim]Chunks Found: {metadata.get('chunks_found', 0)}[/dim]")
                    console.print(f"[dim]Similarity Threshold: {metadata.get('similarity_threshold_used', 0):.2f}[/dim]")
                    console.print(f"[dim]Question Type: {metadata.get('question_type', 'Unknown')}[/dim]")
                
                # Display sources
                if context and 'sources' in parsed_response:
                    sources = parsed_response['sources']
                    if sources:
                        console.print("\n[bold]Brochure Sources:[/bold]")
                        for i, source in enumerate(sources[:3], 1):
                            score = source.get('similarity_score', 0)
                            text_preview = source.get('text', '')[:150] + "..."
                            console.print(f"[dim]Source {i} (Score: {score:.3f}):[/dim] {text_preview}")
                
            except json.JSONDecodeError:
                # If not JSON, display as plain text
                console.print(Panel(response, title="Brochure Answer", border_style="cyan"))

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)
\<EMAIL>()
def status(
    task_id: str = typer.Argument(..., help="Task ID to check")
):
    """Check the status of a background processing task"""

    try:
        proc = get_processor()
        task_status = proc.get_task_status(task_id)

        if not task_status:
            console.print(f"[red]✗[/red] Task not found: {task_id}")
            raise typer.Exit(1)

        # Create status table
        table = Table(title=f"Task Status: {task_id}")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="white")

        status_color = {
            "pending": "yellow",
            "processing": "blue",
            "completed": "green",
            "failed": "red",
            "cancelled": "orange"
        }.get(task_status['status'], "white")

        table.add_row("Status", f"[{status_color}]{task_status['status'].upper()}[/{status_color}]")
        table.add_row("Source", task_status['source'])
        table.add_row("Target Table", task_status['target_table'])
        table.add_row("Progress", f"{task_status['progress']:.1%}")

        if task_status.get('processing_time'):
            table.add_row("Processing Time", f"{task_status['processing_time']:.2f}s")

        if task_status.get('error'):
            table.add_row("Error", f"[red]{task_status['error']}[/red]")

        if task_status.get('result') and task_status['result'].get('chunks_created'):
            table.add_row("Chunks Created", str(task_status['result']['chunks_created']))

        console.print(table)

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)

@app.command()
def stats():
    """Show processing statistics and cache information"""

    try:
        proc = get_processor()
        cache_instance = get_cache()

        # Get processing stats
        proc_stats = proc.get_processing_stats()
        cache_stats = cache_instance.get_cache_stats()

        # Processing statistics table
        proc_table = Table(title="Processing Statistics")
        proc_table.add_column("Metric", style="cyan")
        proc_table.add_column("Value", style="white")

        proc_table.add_row("Total Tasks", str(proc_stats['total_tasks']))
        proc_table.add_row("Completed", f"[green]{proc_stats['completed_tasks']}[/green]")
        proc_table.add_row("Failed", f"[red]{proc_stats['failed_tasks']}[/red]")
        proc_table.add_row("Processing", f"[blue]{proc_stats['processing_tasks']}[/blue]")
        proc_table.add_row("Pending", f"[yellow]{proc_stats['pending_tasks']}[/yellow]")
        proc_table.add_row("Avg Processing Time", f"{proc_stats['average_processing_time']:.2f}s")
        proc_table.add_row("Total Chunks Created", str(proc_stats['total_chunks_created']))

        # Cache statistics table
        cache_table = Table(title="Cache Statistics")
        cache_table.add_column("Metric", style="cyan")
        cache_table.add_column("Value", style="white")

        cache_table.add_row("Cached Documents", str(cache_stats['total_entries']))
        cache_table.add_row("Cache Hits", f"[green]{cache_stats['cache_hits']}[/green]")
        cache_table.add_row("Cache Misses", f"[red]{cache_stats['cache_misses']}[/red]")
        cache_table.add_row("Hit Rate", f"{cache_stats['hit_rate_percent']:.1f}%")
        cache_table.add_row("Database Size", f"{cache_stats['database_size_mb']:.2f} MB")

        console.print(proc_table)
        console.print()
        console.print(cache_table)

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)

@app.command()
def tasks():
    """List all background processing tasks"""

    try:
        proc = get_processor()
        all_tasks = proc.get_all_tasks()

        if not all_tasks:
            console.print("[yellow]No tasks found[/yellow]")
            return

        # Create tasks table
        table = Table(title="Background Processing Tasks")
        table.add_column("Task ID", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Source", style="white")
        table.add_column("Progress", style="white")
        table.add_column("Time", style="white")

        for task in sorted(all_tasks, key=lambda x: x.get('start_time', 0), reverse=True)[:10]:  # Show last 10
            task_id_short = task['task_id'][:8] + "..."

            status_color = {
                "pending": "yellow",
                "processing": "blue",
                "completed": "green",
                "failed": "red",
                "cancelled": "orange"
            }.get(task['status'], "white")

            status_display = f"[{status_color}]{task['status'].upper()}[/{status_color}]"

            source_short = task['source']
            if len(source_short) > 30:
                source_short = source_short[:27] + "..."

            progress_display = f"{task['progress']:.1%}"

            time_display = ""
            if task.get('processing_time'):
                time_display = f"{task['processing_time']:.1f}s"
            elif task.get('start_time'):
                elapsed = time.time() - task['start_time']
                time_display = f"{elapsed:.1f}s"

            table.add_row(task_id_short, status_display, source_short, progress_display, time_display)

        console.print(table)

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)

@app.command()
def cancel(
    task_id: str = typer.Argument(..., help="Task ID to cancel")
):
    """Cancel a background processing task"""

    try:
        proc = get_processor()
        success = proc.cancel_task(task_id)

        if success:
            console.print(f"[green]✓[/green] Task cancelled: {task_id}")
        else:
            console.print(f"[yellow]⚠[/yellow] Task not found or cannot be cancelled: {task_id}")

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)

if __name__ == "__main__":
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Unexpected error:[/red] {str(e)}")
        raise typer.Exit(1)
    finally:
        # Cleanup
        if processor:
            processor.shutdown()
