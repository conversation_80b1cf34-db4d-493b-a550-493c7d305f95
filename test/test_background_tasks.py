#!/usr/bin/env python3
"""
Test Script for Background Task Implementation
Verifies that uploads complete immediately while processing continues in background
"""

import asyncio
import time
import httpx
from pathlib import Path

# Test configuration
BASE_URL = "http://localhost:8000/api"
LOGIN_EMAIL = "<EMAIL>"
LOGIN_PASSWORD = "Admin@1234"

async def login_and_get_token():
    """Login and get authentication token"""
    async with httpx.AsyncClient() as client:
        login_data = {
            "email": LOGIN_EMAIL,
            "password": LOGIN_PASSWORD,
            "remember_me": True
        }
        
        response = await client.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result["data"]["access_token"]
        else:
            raise Exception(f"Login failed: {response.text}")

async def test_document_upload_speed():
    """Test that document upload returns immediately"""
    print("🧪 Testing document upload speed...")
    
    # Get auth token
    token = await login_and_get_token()
    headers = {"Authorization": f"Bearer {token}"}
    
    # Create a test file
    test_content = "This is a test document for background processing verification."
    test_file = Path("test_document.txt")
    test_file.write_text(test_content)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Measure upload time
            start_time = time.time()
            
            with open(test_file, "rb") as f:
                files = {"file": ("test_document.txt", f, "text/plain")}
                data = {
                    "name": "Test Document",
                    "description": "Test document for background processing",
                    "is_active": "true"
                }
                
                response = await client.post(
                    f"{BASE_URL}/documents/upload",
                    headers=headers,
                    files=files,
                    data=data
                )
            
            upload_time = time.time() - start_time
            
            print(f"⏱️  Upload completed in {upload_time:.2f} seconds")
            
            if response.status_code == 201:
                result = response.json()
                document_id = result["data"]["id"]
                print(f"✅ Document uploaded successfully: {document_id}")
                print(f"📝 Message: {result['message_description']}")
                
                # Verify upload was fast (should be < 5 seconds)
                if upload_time < 5.0:
                    print("✅ Upload speed test PASSED - Upload completed quickly")
                else:
                    print("❌ Upload speed test FAILED - Upload took too long")
                
                return document_id
            else:
                print(f"❌ Upload failed: {response.text}")
                return None
                
    finally:
        # Clean up test file
        if test_file.exists():
            test_file.unlink()

async def test_background_task_monitoring():
    """Test background task monitoring endpoints"""
    print("\n🧪 Testing background task monitoring...")
    
    # Get auth token
    token = await login_and_get_token()
    headers = {"Authorization": f"Bearer {token}"}
    
    async with httpx.AsyncClient() as client:
        # Test health endpoint
        response = await client.get(f"{BASE_URL}/background-tasks/health", headers=headers)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ Task system health: {health['data']['status']}")
            print(f"📊 Queue length: {health['data']['queue_length']}")
            print(f"🏃 Active tasks: {health['data']['active_tasks_count']}")
        else:
            print(f"❌ Health check failed: {response.text}")
        
        # Test active tasks endpoint
        response = await client.get(f"{BASE_URL}/background-tasks/active", headers=headers)
        if response.status_code == 200:
            active = response.json()
            print(f"✅ Active tasks retrieved: {active['data']['count']} tasks")
        else:
            print(f"❌ Active tasks check failed: {response.text}")

async def test_document_processing_status(document_id):
    """Test document processing status tracking"""
    if not document_id:
        print("⏭️  Skipping processing status test (no document ID)")
        return
    
    print(f"\n🧪 Testing document processing status for {document_id}...")
    
    # Get auth token
    token = await login_and_get_token()
    headers = {"Authorization": f"Bearer {token}"}
    
    async with httpx.AsyncClient() as client:
        # Check document status multiple times
        for i in range(5):
            response = await client.get(f"{BASE_URL}/documents/{document_id}", headers=headers)
            if response.status_code == 200:
                doc = response.json()
                status = doc["data"]["processing_status"]
                progress = doc["data"].get("processing_progress", 0)
                message = doc["data"].get("processing_message", "")
                
                print(f"📊 Status check {i+1}: {status} ({progress}%) - {message}")
                
                if status in ["processed", "failed"]:
                    print(f"✅ Processing completed with status: {status}")
                    break
            else:
                print(f"❌ Status check failed: {response.text}")
            
            # Wait before next check
            await asyncio.sleep(2)

async def main():
    """Run all tests"""
    print("🚀 Starting Background Task Implementation Tests")
    print("=" * 60)
    
    try:
        # Test 1: Upload speed
        document_id = await test_document_upload_speed()
        
        # Test 2: Background task monitoring
        await test_background_task_monitoring()
        
        # Test 3: Processing status tracking
        await test_document_processing_status(document_id)
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed!")
        print("\n📋 Test Summary:")
        print("✅ Upload speed test - Verifies immediate response")
        print("✅ Task monitoring test - Verifies background task APIs")
        print("✅ Status tracking test - Verifies processing progress")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure FastAPI server is running on localhost:8000")
        print("2. Ensure background services are running (./scripts/start_background_services.sh)")
        print("3. Verify login credentials are correct")

if __name__ == "__main__":
    asyncio.run(main())
