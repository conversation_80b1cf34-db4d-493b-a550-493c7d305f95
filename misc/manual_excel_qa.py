#!/usr/bin/env python3
"""
Manual Excel Q&A - Simple interface for asking questions about Excel data.
"""

import os

try:
    import pandas as pd
    import openai
    LIBS_AVAILABLE = True
except ImportError:
    LIBS_AVAILABLE = False


def load_excel_data():
    """Load and analyze the Excel file."""
    try:
        print("📊 Loading Excel file: xcell_graphs.xlsx")
        
        # Read all sheets
        excel_data = pd.read_excel('xcell_graphs.xlsx', sheet_name=None)
        
        print("✅ Excel file loaded successfully!")
        print(f"📋 Total sheets: {len(excel_data)}")
        
        # Create summary
        summary_parts = ["=== EXCEL FILE: graphs for Xcell to look at.xlsx ===\n"]
        
        for sheet_name, df in excel_data.items():
            summary_parts.append(f"SHEET: {sheet_name}")
            summary_parts.append(f"Size: {len(df)} rows × {len(df.columns)} columns")
            summary_parts.append(f"Columns: {list(df.columns)}")
            
            if len(df) > 0:
                summary_parts.append("Sample Data:")
                summary_parts.append(df.head(3).to_string())
            
            summary_parts.append("")
        
        return "\n".join(summary_parts)
        
    except Exception as e:
        return f"❌ Error loading Excel file: {e}"


def ask_question(question, excel_summary):
    """Ask a question about the Excel data."""
    try:
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            return "❌ OPENAI_API_KEY not set"
        
        client = openai.OpenAI(api_key=api_key)
        
        prompt = f"""
Based on this Excel file data, answer the question:

{excel_summary}

Question: {question}

Answer:"""
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system", 
                    "content": "You are an expert data analyst. Answer questions about Excel data accurately based on the provided spreadsheet content."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            max_tokens=1000,
            temperature=0.1,
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception as e:
        return f"❌ Error: {e}"


def main():
    """Main interface."""
    if not LIBS_AVAILABLE:
        print("❌ Required libraries not available")
        return
    
    if not os.path.exists('../xcell_graphs.xlsx'):
        print("❌ Excel file 'xcell_graphs.xlsx' not found")
        return
    
    # Load Excel data
    excel_summary = load_excel_data()
    
    print("\n🤖 Manual Excel Q&A System")
    print("📄 File: graphs for Xcell to look at.xlsx")
    print("💡 Ask questions about the Excel data. Type 'quit' to exit.")
    print("=" * 60)
    
    while True:
        try:
            question = input("\n❓ Your question: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not question:
                continue
            
            print("\n💭 Answer:")
            answer = ask_question(question, excel_summary)
            print(answer)
            print("\n" + "-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
