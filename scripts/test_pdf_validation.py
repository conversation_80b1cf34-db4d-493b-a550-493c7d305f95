"""
Tests for PDF-only file validation
Tests the restriction to PDF files only for document ingestion
"""

import pytest
from unittest.mock import patch
from fastapi import H<PERSON><PERSON><PERSON><PERSON><PERSON>, UploadFile
from io import BytesIO

from app.services.s3_service import S3Service
from app.services.local_file_service import LocalFileService


class TestPDFValidation:
    """Test PDF file validation across services"""
    
    @pytest.fixture
    def s3_service(self):
        """Create S3 service for testing"""
        with patch('app.services.s3_service.boto3.client'):
            return S3Service()
    
    @pytest.fixture
    def local_file_service(self):
        """Create local file service for testing"""
        return LocalFileService()
    
    def create_mock_upload_file(self, filename: str, content: bytes = b"test content") -> UploadFile:
        """Create a mock UploadFile for testing"""
        file_obj = BytesIO(content)
        upload_file = UploadFile(filename=filename, file=file_obj)
        upload_file.size = len(content)
        upload_file.content_type = "application/pdf" if filename.endswith('.pdf') else "application/octet-stream"
        return upload_file
    
    @pytest.mark.asyncio
    async def test_s3_service_accepts_pdf(self, s3_service):
        """Test that S3 service accepts PDF files"""
        pdf_file = self.create_mock_upload_file("test_document.pdf")
        
        with patch.object(s3_service.s3_client, 'put_object') as mock_put:
            mock_put.return_value = None
            
            # Should not raise exception for PDF
            result = await s3_service.upload_file(pdf_file, prefix="test")
            assert result is not None
            assert "test_document" in result
    
    @pytest.mark.asyncio
    async def test_s3_service_rejects_non_pdf(self, s3_service):
        """Test that S3 service rejects non-PDF files"""
        doc_file = self.create_mock_upload_file("test_document.docx")
        
        with pytest.raises(HTTPException) as exc_info:
            await s3_service.upload_file(doc_file, prefix="test")
        
        assert exc_info.value.status_code == 400
        assert "Only PDF files are allowed" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_s3_service_rejects_image_files(self, s3_service):
        """Test that S3 service rejects image files"""
        image_file = self.create_mock_upload_file("test_image.jpg")
        
        with pytest.raises(HTTPException) as exc_info:
            await s3_service.upload_file(image_file, prefix="test")
        
        assert exc_info.value.status_code == 400
        assert "Only PDF files are allowed" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_local_service_accepts_pdf(self, local_file_service):
        """Test that local file service accepts PDF files"""
        pdf_file = self.create_mock_upload_file("test_document.pdf")
        
        with patch('builtins.open', mock_open=True):
            with patch('os.makedirs'):
                with patch('os.path.exists', return_value=True):
                    # Should not raise exception for PDF
                    result = await local_file_service.upload_file(pdf_file, prefix="test")
                    assert result is not None
    
    @pytest.mark.asyncio
    async def test_local_service_rejects_non_pdf(self, local_file_service):
        """Test that local file service rejects non-PDF files"""
        doc_file = self.create_mock_upload_file("test_document.docx")
        
        with pytest.raises(HTTPException) as exc_info:
            await local_file_service.upload_file(doc_file, prefix="test")
        
        assert exc_info.value.status_code == 400
        assert "Only PDF files are allowed" in str(exc_info.value.detail)
    
    def test_pdf_extension_validation(self):
        """Test PDF extension validation logic"""
        import os
        
        # Test valid PDF extensions
        valid_files = ["document.pdf", "Document.PDF", "test.Pdf"]
        for filename in valid_files:
            extension = os.path.splitext(filename)[1].lower()
            assert extension == '.pdf'
        
        # Test invalid extensions
        invalid_files = ["document.docx", "image.jpg", "text.txt", "presentation.pptx"]
        for filename in invalid_files:
            extension = os.path.splitext(filename)[1].lower()
            assert extension != '.pdf'
    
    @pytest.mark.asyncio
    async def test_file_size_validation_still_works(self, s3_service):
        """Test that file size validation still works with PDF restriction"""
        # Create a large PDF file (over 10MB)
        large_content = b"x" * (11 * 1024 * 1024)  # 11MB
        large_pdf = self.create_mock_upload_file("large_document.pdf", large_content)
        
        with pytest.raises(HTTPException) as exc_info:
            await s3_service.upload_file(large_pdf, prefix="test")
        
        assert exc_info.value.status_code == 400
        assert "File size exceeds 10MB limit" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_empty_filename_validation(self, s3_service):
        """Test that empty filename validation still works"""
        empty_file = UploadFile(filename="", file=BytesIO(b"content"))
        
        with pytest.raises(HTTPException) as exc_info:
            await s3_service.upload_file(empty_file, prefix="test")
        
        assert exc_info.value.status_code == 400
        assert "Invalid file" in str(exc_info.value.detail)


class TestDocumentToolsPDFValidation:
    """Test PDF validation in document tools"""
    
    @pytest.mark.asyncio
    async def test_extract_text_tool_pdf_only(self):
        """Test that extract text tool only processes PDF files"""
        from app.agents.tools.document_tools import ExtractTextTool
        
        tool = ExtractTextTool()
        
        # Test PDF file (should work)
        pdf_result = await tool._arun(file_path="/path/to/document.pdf")
        assert "Only PDF files are supported" not in pdf_result
        
        # Test non-PDF file (should be rejected)
        docx_result = await tool._arun(file_path="/path/to/document.docx")
        assert "Only PDF files are supported" in docx_result
        
        # Test image file (should be rejected)
        jpg_result = await tool._arun(file_path="/path/to/image.jpg")
        assert "Only PDF files are supported" in jpg_result


class TestFranchisorEndpointPDFValidation:
    """Test PDF validation in franchisor endpoints"""
    
    def test_endpoint_documentation_updated(self):
        """Test that endpoint documentation reflects PDF-only restriction"""
        # This is more of a documentation test
        # In a real scenario, you'd test the actual endpoint responses
        
        # The upload_brochure endpoint should now specify "PDF only"
        # The update_franchisor endpoint should specify "PDF only"
        
        # This test ensures the documentation is consistent
        assert True  # Placeholder - in real implementation, test actual endpoint docs


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
