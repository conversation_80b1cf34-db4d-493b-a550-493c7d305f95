#!/bin/bash
# Build cleanup script to reduce staging server storage from 44GB

echo "🧹 Starting build cleanup to reduce 44GB storage usage..."

# 1. Remove Python cache files (can save 1-2GB)
echo "Removing Python cache files..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyo" -delete

# 2. Remove pip cache (can save 5-10GB)
echo "Clearing pip cache..."
pip cache purge

# 3. Remove build artifacts
echo "Removing build artifacts..."
find . -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true

# 4. Remove test artifacts
echo "Removing test artifacts..."
find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".coverage" -delete 2>/dev/null || true
find . -name "htmlcov" -type d -exec rm -rf {} + 2>/dev/null || true

# 5. Remove IDE and editor files
echo "Removing IDE files..."
find . -name ".vscode" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".idea" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.swp" -delete
find . -name "*.swo" -delete
find . -name "*~" -delete

# 6. Remove log files
echo "Removing log files..."
find . -name "*.log" -delete
find . -name "logs" -type d -exec rm -rf {} + 2>/dev/null || true

# 7. Remove temporary files
echo "Removing temporary files..."
find . -name "tmp" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "temp" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".tmp" -type d -exec rm -rf {} + 2>/dev/null || true

echo "✅ Cleanup completed!"
echo "Run 'du -sh .' to check new size"
