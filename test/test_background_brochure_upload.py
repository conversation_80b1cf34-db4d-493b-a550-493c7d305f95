#!/usr/bin/env python3
"""
Test Background Brochure Upload Processing
Demonstrates the new background processing functionality for franchisor brochure uploads
"""

import asyncio
import httpx
import time
import json
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
TEST_PDF_PATH = "franchise_brochure.pdf"  # Ensure this file exists
FRANCHISOR_ID = "frc_123456789"  # Replace with actual franchisor ID

async def test_background_brochure_upload():
    """Test the background processing brochure upload endpoint"""
    
    print("🧪 Testing Background Brochure Upload Processing")
    print("=" * 60)
    
    # Check if test PDF exists
    if not Path(TEST_PDF_PATH).exists():
        print(f"❌ Test PDF file not found: {TEST_PDF_PATH}")
        print("   Please ensure a test PDF file exists in the project root")
        return
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # Step 1: Create a test user and get auth token (if needed)
        print("1️⃣ Setting up authentication...")
        
        # For this test, we'll assume authentication is handled
        # In a real scenario, you'd get a JWT token here
        headers = {
            # "Authorization": "Bearer your-jwt-token-here"
        }
        
        # Step 2: Check if franchisor exists
        print(f"2️⃣ Checking franchisor {FRANCHISOR_ID}...")
        
        try:
            response = await client.get(
                f"{BASE_URL}/api/franchisors/{FRANCHISOR_ID}",
                headers=headers
            )
            
            if response.status_code == 404:
                print(f"❌ Franchisor {FRANCHISOR_ID} not found")
                print("   Please create a franchisor first or update FRANCHISOR_ID")
                return
            elif response.status_code != 200:
                print(f"❌ Error checking franchisor: {response.status_code}")
                print(f"   Response: {response.text}")
                return
                
            franchisor_data = response.json()
            print(f"✅ Franchisor found: {franchisor_data.get('data', {}).get('name', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Error checking franchisor: {e}")
            return
        
        # Step 3: Upload brochure with background processing
        print("3️⃣ Uploading brochure with background processing...")
        
        try:
            with open(TEST_PDF_PATH, "rb") as pdf_file:
                files = {"file": ("test_brochure.pdf", pdf_file, "application/pdf")}
                
                response = await client.post(
                    f"{BASE_URL}/api/franchisors/{FRANCHISOR_ID}/upload-brochure",
                    files=files,
                    headers=headers
                )
            
            if response.status_code != 200:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return
            
            upload_result = response.json()
            print("✅ Brochure uploaded successfully!")
            
            # Extract task information
            task_id = upload_result.get("data", {}).get("processing_task_id")
            processing_status = upload_result.get("data", {}).get("processing_status")
            
            print(f"   Task ID: {task_id}")
            print(f"   Status: {processing_status}")
            print(f"   Message: {upload_result.get('message', {}).get('description', '')}")
            
            if not task_id:
                print("⚠️  No task ID returned - processing may have failed to queue")
                return
                
        except Exception as e:
            print(f"❌ Error uploading brochure: {e}")
            return
        
        # Step 4: Monitor task progress
        print("4️⃣ Monitoring task progress...")
        
        max_wait_time = 300  # 5 minutes
        check_interval = 5   # 5 seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                response = await client.get(
                    f"{BASE_URL}/api/background-tasks/status/{task_id}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    task_status = response.json()
                    status = task_status.get("status", "UNKNOWN")
                    progress = task_status.get("progress", 0)
                    
                    print(f"   Status: {status} | Progress: {progress}%")
                    
                    if status in ["SUCCESS", "PROCESSED"]:
                        print("✅ Task completed successfully!")
                        result = task_status.get("result", {})
                        print(f"   DocQA Success: {result.get('docqa_success', False)}")
                        print(f"   Agent Success: {result.get('agent_success', False)}")
                        if result.get('processing_details'):
                            details = result['processing_details']
                            print(f"   Chunks Created: {details.get('docqa_chunks_created', 0)}")
                        break
                    elif status in ["FAILURE", "FAILED"]:
                        print("❌ Task failed!")
                        error = task_status.get("error", "Unknown error")
                        print(f"   Error: {error}")
                        break
                    elif status in ["RETRY"]:
                        print("🔄 Task is retrying...")
                    
                else:
                    print(f"⚠️  Could not get task status: {response.status_code}")
                
            except Exception as e:
                print(f"⚠️  Error checking task status: {e}")
            
            await asyncio.sleep(check_interval)
        else:
            print("⏰ Task monitoring timed out")
        
        # Step 5: Verify franchisor was updated
        print("5️⃣ Verifying franchisor update...")
        
        try:
            response = await client.get(
                f"{BASE_URL}/api/franchisors/{FRANCHISOR_ID}",
                headers=headers
            )
            
            if response.status_code == 200:
                updated_franchisor = response.json()
                brochure_url = updated_franchisor.get("data", {}).get("brochure_url")
                
                if brochure_url:
                    print(f"✅ Franchisor updated with brochure URL: {brochure_url}")
                else:
                    print("⚠️  Franchisor brochure URL not updated")
            else:
                print(f"⚠️  Could not verify franchisor update: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️  Error verifying franchisor update: {e}")
    
    print("\n🎉 Background brochure upload test completed!")

def main():
    """Main function"""
    print("GrowthHive Background Brochure Upload Test")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    print("Make sure RabbitMQ, Redis, and Celery worker are running")
    print()
    
    # Run the async test
    asyncio.run(test_background_brochure_upload())

if __name__ == "__main__":
    main()
