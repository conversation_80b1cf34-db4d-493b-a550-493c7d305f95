"""
Question Classification Agent
Classifies incoming questions into answerable vs escalation categories
"""

from typing import Dict, Any, List, Optional
import structlog
import json
from langchain_core.messages import AIMessage

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry
from docqa.central_api import ask_question

logger = structlog.get_logger()


class QuestionClassificationAgent(BaseAgent):
    """
    Agent responsible for classifying questions into:
    1. Valid, answerable questions → question_bank
    2. Unanswerable, unclear, or out-of-scope questions → escalation_question_bank
    """
    
    def _initialize_tools(self):
        """Initialize classification-specific tools"""
        # Import and register question bank tools
        from .tools.question_bank_tools import (
            InsertQuestionBankTool,
            InsertEscalationQuestionBankTool,
            AnalyzeQuestionTool
        )
        
        # Register the tools in the registry
        tool_registry.register_tool("insert_question_bank", InsertQuestionBankTool)
        tool_registry.register_tool("insert_escalation_question_bank", InsertEscalationQuestionBankTool)
        tool_registry.register_tool("analyze_question", AnalyzeQuestionTool)
        
        # Get tools for this agent
        tool_names = [
            "insert_question_bank", 
            "insert_escalation_question_bank", 
            "analyze_question",
            "search_documents"
        ]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process question classification requests"""
        try:
            question_text = state.get("user_input", "")
            context = state.get("context", {})
            lead_id = state.get("lead_id")
            franchisor_id = state.get("franchisor_id")
            
            if not question_text:
                state["error"] = "No question text provided"
                return state
            
            logger.info(f"Classifying question: {question_text}")

            # Check if we have AI response from context (brochure-based classification)
            ai_response = context.get("ai_response", "") if context else ""

            if ai_response and context.get("brochure_based_classification"):
                # Step 1: Use actual AI response for brochure-based classification
                logger.info(f"Using AI response for brochure-based classification")
                analysis_result = await self._analyze_question_with_rag(question_text, context, ai_response)
                state["analysis"] = analysis_result
                state["rag_answer"] = ai_response

                # Step 2: Classify and insert based on AI response quality
                classification_result = await self._classify_and_insert(
                    question_text, lead_id, franchisor_id, analysis_result, ai_response
                )
            else:
                # Step 1: Try to answer using RAG/brochure system to determine if answerable
                rag_answer = await self._attempt_rag_answer(question_text, franchisor_id)
                state["rag_answer"] = rag_answer

                # Step 2: Analyze based on RAG answer quality (brochure-based classification)
                analysis_result = await self._analyze_question_with_rag(question_text, context, rag_answer)
                state["analysis"] = analysis_result

                # Step 3: Classify and insert into appropriate table based on brochure search results
                classification_result = await self._classify_and_insert(
                    question_text, lead_id, franchisor_id, analysis_result, rag_answer
                )

            state["classification_result"] = classification_result
            state["response"] = classification_result

            return state
            
        except Exception as e:
            logger.error(f"Error in question classification: {str(e)}")
            state["error"] = str(e)
            return state
    
    async def _attempt_rag_answer(self, question: str, franchisor_id: Optional[str] = None) -> str:
        """Attempt to answer the question using the RAG system"""
        try:
            # Prepare request for DocQA system
            request = {
                "question": question,
                "top_k": 5,
                "similarity_threshold": 0.1,  # Low threshold for better coverage
                "temperature": 0.3,
                "max_tokens": 500
            }
            
            if franchisor_id:
                request["franchisor_id"] = franchisor_id
            
            # Use the central DocQA API
            result = await ask_question(request)
            
            if isinstance(result, dict) and "answer" in result:
                return result["answer"]
            else:
                return "No answer available from knowledge base"
                
        except Exception as e:
            logger.error(f"Error getting RAG answer: {str(e)}")
            return f"Error accessing knowledge base: {str(e)}"
    
    async def _analyze_question_with_rag(self, question: str, context: Dict[str, Any], rag_answer: str) -> Dict[str, Any]:
        """Analyze question based on RAG/brochure search results"""
        try:
            logger.info(f"Analyzing question with RAG answer: '{question}' -> RAG length: {len(rag_answer) if rag_answer else 0}")

            # Check if RAG answer indicates the question can be answered from brochure
            if not rag_answer or len(rag_answer.strip()) < 10:
                logger.info(f"Question classified as ESCALATION: No adequate RAG answer found")
                return {"needs_escalation": True, "reason": "Question not found in brochure/documentation"}

            rag_answer_lower = rag_answer.lower()

            # Check for explicit failure indicators in RAG answer
            failure_phrases = [
                "i don't know", "i cannot answer", "no information available",
                "not found", "unclear", "insufficient information",
                "error accessing knowledge base", "no answer available from knowledge base",
                "rag systems failed", "all available rag systems failed",
                "sorry, i encountered an error", "error while processing",
                "qna system is not available", "system configuration",
                "i don't have access", "i'm not able to", "i cannot provide",
                "no relevant information", "not mentioned in", "not covered in",
                "search failed", "database error", "relation does not exist",
                "unfortunately, i don't have", "i don't have access to",
                "i'm not able to provide", "i can't provide", "i cannot help with",
                "that's outside my knowledge", "not something i can help with",
                "i'd recommend checking", "you should check", "try checking",
                "for today's weather", "for weather information", "weather app or website",
                "for the most accurate", "for current information"
            ]

            # Check for positive indicators that suggest good franchise-related content
            positive_indicators = [
                "franchise fee", "royalty", "investment", "training", "support",
                "territory", "marketing fee", "revenue", "coochie hydrogreen",
                "business opportunity", "franchisor", "franchisee", "$", "gst",
                "monthly", "annual", "percentage", "%"
            ]

            has_failure_indicators = any(phrase in rag_answer_lower for phrase in failure_phrases)
            has_positive_indicators = any(phrase in rag_answer_lower for phrase in positive_indicators)

            # Prioritize failure indicators - if AI says it doesn't know, it's escalation regardless of positive content
            if has_failure_indicators:
                logger.info(f"Question classified as ESCALATION: AI indicates it cannot answer the question")
                return {"needs_escalation": True, "reason": "Question not adequately answered by brochure/documentation"}
            elif has_positive_indicators and len(rag_answer.strip()) > 50:
                logger.info(f"Question classified as ANSWERABLE: Found franchise-related content in answer")
                return {"needs_escalation": False, "reason": "Question can be answered from brochure/documentation"}
            else:
                # Be more strict - only consider it answerable if it has positive franchise indicators
                # Don't classify based on length alone as it can be misleading
                logger.info(f"Question classified as ESCALATION: No clear franchise-related answer found")
                return {"needs_escalation": True, "reason": "Question not adequately answered by brochure/documentation"}

        except Exception as e:
            logger.error(f"Error analyzing question with RAG: {str(e)}")
            return {"needs_escalation": True, "reason": f"Analysis error: {str(e)}"}

    async def _analyze_question_only(self, question: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze question based ONLY on question text, not on AI answers (legacy method)"""
        try:
            # Use the analyze_question tool but without RAG answer dependency
            analysis_tool = next((tool for tool in self.tools if tool.name == "analyze_question"), None)
            if not analysis_tool:
                return {"needs_escalation": True, "reason": "Analysis tool not available"}

            result = await analysis_tool._arun(
                question_text=question,
                context=str(context),
                rag_answer=""  # Empty RAG answer - classification based only on question
            )

            logger.info(f"Analysis tool result for question '{question}': {result}")

            # Parse the result - handle both dictionary string format and key:value format
            result_lower = result.lower()
            if ("'needs_escalation': true" in result_lower or
                "needs_escalation: true" in result_lower or
                '"needs_escalation": true' in result_lower):
                logger.info(f"Question classified as ESCALATION: {question}")
                return {"needs_escalation": True, "reason": "Question requires human support based on content analysis"}
            else:
                logger.info(f"Question classified as ANSWERABLE: {question}")
                return {"needs_escalation": False, "reason": "Question can be handled automatically"}

        except Exception as e:
            logger.error(f"Error analyzing question: {str(e)}")
            return {"needs_escalation": True, "reason": f"Analysis error: {str(e)}"}

    async def _analyze_question(self, question: str, context: Dict[str, Any], rag_answer: str) -> Dict[str, Any]:
        """Analyze question to determine if it needs escalation (legacy method)"""
        try:
            # Check if there was a processing error first
            if context.get("processing_error") or not context.get("processing_success", True):
                return {
                    "needs_escalation": True,
                    "reason": f"AI/RAG processing failed: {context.get('processing_error', 'Unknown error')}"
                }

            # Use the analyze_question tool
            analysis_tool = next((tool for tool in self.tools if tool.name == "analyze_question"), None)
            if not analysis_tool:
                return {"needs_escalation": True, "reason": "Analysis tool not available"}

            result = await analysis_tool._arun(
                question_text=question,
                context=str(context),
                rag_answer=rag_answer
            )
            
            # Parse the result
            try:
                return eval(result)  # Safe since we control the tool output
            except:
                return {"needs_escalation": True, "reason": "Analysis parsing error"}
                
        except Exception as e:
            logger.error(f"Error analyzing question: {str(e)}")
            return {"needs_escalation": True, "reason": f"Analysis error: {str(e)}"}
    
    async def _classify_and_insert(
        self, 
        question: str, 
        lead_id: Optional[str], 
        franchisor_id: Optional[str],
        analysis: Dict[str, Any],
        rag_answer: str
    ) -> Dict[str, Any]:
        """Classify question and insert into appropriate table"""
        try:
            needs_escalation = analysis.get("needs_escalation", True)
            reason = analysis.get("reason", "Unknown")
            
            if needs_escalation:
                # Insert into escalation_question_bank
                escalation_tool = next((tool for tool in self.tools if tool.name == "insert_escalation_question_bank"), None)
                if escalation_tool:
                    result = await escalation_tool._arun(
                        question_text=question,
                        lead_id=lead_id,
                        franchisor_id=franchisor_id,
                        reason=reason
                    )
                    
                    return {
                        "classification": "escalation",
                        "table": "escalation_question_bank",
                        "reason": reason,
                        "sql_result": result,
                        "rag_answer": rag_answer,
                        "analysis": analysis
                    }
            else:
                # Insert into question_bank
                question_tool = next((tool for tool in self.tools if tool.name == "insert_question_bank"), None)
                if question_tool:
                    result = await question_tool._arun(
                        question_text=question,
                        lead_id=lead_id,
                        franchisor_id=franchisor_id
                    )
                    
                    return {
                        "classification": "answerable",
                        "table": "question_bank",
                        "answer": rag_answer,
                        "sql_result": result,
                        "analysis": analysis
                    }
            
            return {
                "classification": "error",
                "error": "Could not find appropriate tool for insertion"
            }
            
        except Exception as e:
            logger.error(f"Error in classification and insertion: {str(e)}")
            return {
                "classification": "error",
                "error": str(e)
            }
    
    async def classify_question(
        self, 
        question_text: str, 
        lead_id: Optional[str] = None, 
        franchisor_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Main method to classify a question
        
        Args:
            question_text: The question to classify
            lead_id: Optional lead ID
            franchisor_id: Optional franchisor ID
            context: Optional context information
            
        Returns:
            Classification result with SQL statements
        """
        try:
            # Prepare state
            state: AgentState = {
                "user_input": question_text,
                "lead_id": lead_id,
                "franchisor_id": franchisor_id,
                "context": context or {},
                "messages": [],
                "session_id": f"classification_{lead_id or 'unknown'}",
                "intent": "classify_question",
                "next_action": None,
                "lead_data": None,
                "lead_status": None,
                "document_id": None,
                "document_content": None,
                "search_results": None,
                "meeting_data": None,
                "availability": None,
                "conversation_history": [],
                "response": None,
                "error": None,
                "metadata": {"timestamp": None}
            }
            
            # Process the classification
            result_state = await self.process_state(state)
            
            if result_state.get("error"):
                return {
                    "success": False,
                    "error": result_state["error"]
                }
            
            return {
                "success": True,
                "result": result_state.get("classification_result", {}),
                "rag_answer": result_state.get("rag_answer"),
                "analysis": result_state.get("analysis")
            }
            
        except Exception as e:
            logger.error(f"Error in classify_question: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
