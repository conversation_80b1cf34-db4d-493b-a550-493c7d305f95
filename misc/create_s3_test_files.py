#!/usr/bin/env python3
"""
Create test files and upload to S3 for DocQA testing
"""

import sys
import tempfile
from pathlib import Path
import boto3
from PIL import Image, ImageDraw, ImageFont

# Add project root to path
sys.path.append('..')

def create_test_pdf():
    """Create a test PDF document"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            c = canvas.Canvas(tmp_file.name, pagesize=letter)
            
            # Add franchise content
            c.drawString(100, 750, "COFFEE MASTERS FRANCHISE OPPORTUNITY")
            c.drawString(100, 720, "Premium Coffee Franchise - Melbourne Region")
            c.drawString(100, 680, "Investment Details:")
            c.drawString(120, 660, "• Initial Investment: $80,000 - $150,000")
            c.drawString(120, 640, "• Franchise Fee: $45,000")
            c.drawString(120, 620, "• Royalty: 6% of gross sales")
            c.drawString(120, 600, "• Marketing Fee: 2% of gross sales")
            
            c.drawString(100, 560, "What We Provide:")
            c.drawString(120, 540, "• 3 weeks comprehensive training")
            c.drawString(120, 520, "• Site selection assistance")
            c.drawString(120, 500, "• Equipment package")
            c.drawString(120, 480, "• Ongoing operational support")
            c.drawString(120, 460, "• Marketing materials and campaigns")
            
            c.drawString(100, 420, "Contact Information:")
            c.drawString(120, 400, "Phone: (03) 9876-5432")
            c.drawString(120, 380, "Email: <EMAIL>")
            c.drawString(120, 360, "Website: www.coffeemasters.com.au/franchise")
            c.drawString(120, 340, "Address: 123 Business St, Melbourne VIC 3000")
            
            c.save()
            return Path(tmp_file.name)
            
    except ImportError:
        print("⚠️  reportlab not available, skipping PDF creation")
        return None


def create_test_docx():
    """Create a test DOCX document"""
    try:
        from docx import Document
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp_file:
            doc = Document()
            
            # Add franchise content
            doc.add_heading('PIZZA EXPRESS FRANCHISE GUIDE', 0)
            
            doc.add_heading('Investment Overview', level=1)
            doc.add_paragraph('Pizza Express is seeking qualified franchisees for our proven pizza restaurant concept.')
            
            # Add investment table
            table = doc.add_table(rows=6, cols=2)
            table.style = 'Table Grid'
            
            investments = [
                ('Investment Component', 'Amount'),
                ('Initial Franchise Fee', '$55,000'),
                ('Equipment & Fit-out', '$120,000 - $180,000'),
                ('Working Capital', '$30,000 - $50,000'),
                ('Marketing Launch', '$15,000'),
                ('Total Investment', '$220,000 - $340,000')
            ]
            
            for i, (component, amount) in enumerate(investments):
                table.rows[i].cells[0].text = component
                table.rows[i].cells[1].text = amount
            
            doc.add_heading('Franchise Support', level=1)
            support_items = [
                'Site selection and lease negotiation assistance',
                'Store design and construction management',
                'Comprehensive 4-week training program',
                'Grand opening marketing support',
                'Ongoing operational and marketing support',
                'Supply chain management and purchasing power'
            ]
            
            for item in support_items:
                doc.add_paragraph(item, style='List Bullet')
            
            doc.add_heading('Contact Details', level=1)
            doc.add_paragraph('Pizza Express Franchise Development')
            doc.add_paragraph('Phone: (02) 8765-4321')
            doc.add_paragraph('Email: <EMAIL>')
            doc.add_paragraph('Website: www.pizzaexpress.com.au/franchise')
            
            doc.save(tmp_file.name)
            return Path(tmp_file.name)
            
    except ImportError:
        print("⚠️  python-docx not available, skipping DOCX creation")
        return None


def create_test_image():
    """Create a test image with franchise information"""
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
        # Create a professional-looking franchise image
        img = Image.new('RGB', (1000, 700), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a system font
        try:
            title_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 32)
            header_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
            body_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
        except:
            title_font = header_font = body_font = ImageFont.load_default()
        
        y = 50
        
        # Title
        draw.text((50, y), "BURGER KINGDOM FRANCHISE", fill='#1a472a', font=title_font)
        y += 60
        
        # Subtitle
        draw.text((50, y), "Fast Food Franchise Opportunity - Sydney Region", fill='#2d5a3d', font=header_font)
        y += 80
        
        # Investment details
        sections = [
            ("TOTAL INVESTMENT", "$200,000 - $350,000"),
            ("FRANCHISE FEE", "$60,000"),
            ("EQUIPMENT PACKAGE", "$100,000 - $150,000"),
            ("WORKING CAPITAL", "$40,000 - $80,000"),
            ("ROYALTY FEE", "7% of gross sales"),
            ("ADVERTISING FEE", "3% of gross sales"),
        ]
        
        for section, detail in sections:
            draw.text((50, y), section, fill='#1a472a', font=header_font)
            draw.text((400, y), detail, fill='#2d5a3d', font=body_font)
            y += 35
        
        # Support information
        y += 30
        draw.text((50, y), "FRANCHISE SUPPORT INCLUDES:", fill='#1a472a', font=header_font)
        y += 40
        
        support_items = [
            "• Site selection and lease negotiation",
            "• Restaurant design and construction",
            "• 3 weeks training at head office",
            "• Grand opening marketing campaign",
            "• Ongoing operational support",
            "• Supply chain and vendor relationships"
        ]
        
        for item in support_items:
            draw.text((50, y), item, fill='#2d5a3d', font=body_font)
            y += 25
        
        # Contact info
        y += 30
        draw.text((50, y), "CONTACT: <EMAIL> | (02) 9876-5432", fill='#1a472a', font=header_font)
        
        img.save(tmp_file.name)
        return Path(tmp_file.name)


def upload_to_s3(file_path: Path, bucket_name: str, s3_key: str):
    """Upload file to S3"""
    try:
        s3_client = boto3.client('s3')
        
        print(f"Uploading {file_path.name} to s3://{bucket_name}/{s3_key}")
        
        s3_client.upload_file(
            str(file_path), 
            bucket_name, 
            s3_key,
            ExtraArgs={'ContentType': get_content_type(file_path)}
        )
        
        print(f"✅ Successfully uploaded to s3://{bucket_name}/{s3_key}")
        return f"s3://{bucket_name}/{s3_key}"
        
    except Exception as e:
        print(f"❌ Failed to upload {file_path.name}: {e}")
        return None


def get_content_type(file_path: Path) -> str:
    """Get content type for S3 upload"""
    suffix = file_path.suffix.lower()
    content_types = {
        '.pdf': 'application/pdf',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg'
    }
    return content_types.get(suffix, 'application/octet-stream')


def main():
    """Create test files and upload to S3"""
    print("🚀 Creating Test Files for DocQA S3 Testing")
    print("=" * 50)
    
    # Get S3 bucket name
    bucket_name = input("Enter your S3 bucket name: ").strip()
    if not bucket_name:
        print("❌ Bucket name is required")
        return
    
    # Check AWS credentials
    try:
        boto3.client('s3').head_bucket(Bucket=bucket_name)
        print(f"✅ S3 bucket '{bucket_name}' is accessible")
    except Exception as e:
        print(f"❌ Cannot access S3 bucket '{bucket_name}': {e}")
        print("Make sure AWS credentials are configured (aws configure)")
        return
    
    test_files = []
    s3_urls = []
    
    # Create test files
    print("\n📄 Creating test documents...")
    
    pdf_file = create_test_pdf()
    if pdf_file:
        test_files.append((pdf_file, "test-documents/coffee-masters-franchise.pdf"))
    
    docx_file = create_test_docx()
    if docx_file:
        test_files.append((docx_file, "test-documents/pizza-express-guide.docx"))
    
    image_file = create_test_image()
    if image_file:
        test_files.append((image_file, "test-documents/burger-kingdom-brochure.png"))
    
    # Upload to S3
    print("\n☁️  Uploading to S3...")
    
    for file_path, s3_key in test_files:
        s3_url = upload_to_s3(file_path, bucket_name, s3_key)
        if s3_url:
            s3_urls.append(s3_url)
        
        # Clean up local file
        try:
            file_path.unlink()
        except:
            pass
    
    # Provide testing commands
    if s3_urls:
        print("\n🎯 Test Commands:")
        print("Now you can test DocQA with these S3 URLs:")
        print()
        
        for s3_url in s3_urls:
            print("# Test ingestion:")
            print(f"python3 -m docqa.cli ingest {s3_url}")
            print()
        
        print("# Test question answering after ingestion:")
        print("python3 -m docqa.cli ask 'What franchise opportunities are available?'")
        print("python3 -m docqa.cli ask 'What are the investment requirements?'")
        print("python3 -m docqa.cli ask 'What support is provided to franchisees?'")
        print()
        
        print("# Test with options:")
        print(f"python3 -m docqa.cli ingest {s3_urls[0]} --table franchisors --extract-charts --verbose")
    
    print("\n🎉 S3 test files created successfully!")


if __name__ == "__main__":
    main()
